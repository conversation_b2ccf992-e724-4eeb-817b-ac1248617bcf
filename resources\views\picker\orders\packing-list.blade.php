<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packing List - Order #{{$order->order_number}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4D734E;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .order-info div {
            flex: 1;
        }
        .customer-info {
            background: #f8f9fa;
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .customer-info h3 {
            margin-top: 0;
            color: #4D734E;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .items-table th {
            background-color: #4D734E;
            color: white;
            font-weight: bold;
        }
        .items-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        .signature-box {
            width: 200px;
            border-bottom: 1px solid #000;
            text-align: center;
            padding-top: 40px;
        }
        .notes-section {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            min-height: 80px;
        }
        .notes-section h4 {
            margin-top: 0;
            color: #4D734E;
        }
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            .no-print {
                display: none;
            }
        }
        .badge {
            display: inline-block;
            padding: 3px 6px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 3px;
            color: white;
        }
        .badge-info { background-color: #17a2b8; }
        .badge-success { background-color: #28a745; }
        .badge-warning { background-color: #ffc107; color: #000; }
        .badge-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">LAMART</div>
        <div class="document-title">PACKING LIST</div>
        <div>Order #{{$order->order_number}} | Quote #{{$order->quote_number}} | Date: {{$order->created_at->format('M d, Y')}}</div>
    </div>

    <div class="order-info">
        <div>
            <strong>Order Number:</strong> {{$order->order_number}}<br>
            <strong>Quote Number:</strong> {{$order->quote_number}}<br>
            <strong>Order Date:</strong> {{$order->created_at->format('M d, Y h:i A')}}<br>
            <strong>Picker:</strong> {{Auth::user()->first_name}} {{Auth::user()->last_name}}<br>
            <strong>Delivery Method:</strong> {{ucfirst($order->delivery_method ?? 'N/A')}}
        </div>
        <div>
            <strong>Total Items:</strong> {{$order->cart_info->count()}}<br>
            <strong>Total Amount:</strong> ${{number_format($order->total_amount,2)}}<br>
            <strong>Status:</strong> {{ucfirst($order->status)}}<br>
            <strong>Weight:</strong> {{$order->total_weight ?? 'TBD'}}
        </div>
    </div>

    <div class="customer-info">
        <h3>Customer Information</h3>
        <div style="display: flex; justify-content: space-between;">
            <div>
                <strong>Name:</strong> {{$order->first_name}} {{$order->last_name}}<br>
                <strong>Email:</strong> {{$order->email}}<br>
                <strong>Phone:</strong> {{$order->phone}}
            </div>
            <div>
                <strong>Shipping Address:</strong><br>
                {{$order->address1}}<br>
                @if($order->address2){{$order->address2}}<br>@endif
                {{$order->country}}, {{$order->post_code}}
            </div>
        </div>
        @if($order->delivery_instructions)
        <div style="margin-top: 10px;">
            <strong>Delivery Instructions:</strong> {{$order->delivery_instructions}}
        </div>
        @endif
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Item #</th>
                <th>Product Description</th>
                <th>Color</th>
                <th>Ordered Qty</th>
                <th>Packed Qty</th>
                <th>Box</th>
                <th>Status</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->cart_info as $item)
            <tr>
                <td>{{$item->product->item_number ?? 'N/A'}}</td>
                <td>
                    <strong>{{$item->product->title}}</strong><br>
                    <small>{{$item->product->summary}}</small>
                </td>
                <td>{{$item->color_name->name ?? 'N/A'}}</td>
                <td>{{$item->quantity}}</td>
                <td>{{$item->quantity}}</td>
                <td>Box {{$item->box}}</td>
                <td>
                    @if($item->is_packed)
                        <span class="badge badge-success">Packed</span>
                    @elseif($item->is_returned_by_picker)
                        <span class="badge badge-danger">Returned</span>
                    @else
                        <span class="badge badge-warning">Pending</span>
                    @endif
                </td>
                <td style="width: 100px; border-bottom: 1px dotted #ccc;"></td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <div style="display: flex; justify-content: space-between;">
            <div>
                <strong>Total Items:</strong> {{$order->cart_info->count()}}<br>
                <strong>Total Boxes:</strong> {{$order->cart_info->pluck('box')->unique()->count()}}<br>
                <strong>Packed Items:</strong> {{$order->cart_info->where('is_packed', true)->count()}}
            </div>
            <div>
                <strong>Returned Items:</strong> {{$order->cart_info->where('is_returned_by_picker', true)->count()}}<br>
                <strong>Pending Items:</strong> {{$order->cart_info->where('is_packed', false)->where('is_returned_by_picker', false)->count()}}
            </div>
        </div>
    </div>

    <div class="notes-section">
        <h4>Packing Notes:</h4>
        <div style="min-height: 60px; border-bottom: 1px dotted #ccc;"></div>
    </div>

    <div class="signature-section">
        <div>
            <div class="signature-box">Picker Signature</div>
            <div style="text-align: center; margin-top: 5px;">
                {{Auth::user()->first_name}} {{Auth::user()->last_name}}
            </div>
        </div>
        <div>
            <div class="signature-box">Quality Check</div>
            <div style="text-align: center; margin-top: 5px;">
                Date: _______________
            </div>
        </div>
        <div>
            <div class="signature-box">Supervisor Approval</div>
            <div style="text-align: center; margin-top: 5px;">
                Date: _______________
            </div>
        </div>
    </div>

    <div class="no-print" style="margin-top: 20px; text-align: center;">
        <button onclick="window.print()" style="padding: 10px 20px; background: #4D734E; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Print Packing List
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>
</body>
</html>
