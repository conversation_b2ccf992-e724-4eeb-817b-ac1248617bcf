<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packing Slip - Order {{$order->order_number}}</title>
    <style>
        /* 4x6 inch label styling */
        @page {
            size: 4in 6in;
            margin: 0.1in;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 8px;
            width: 3.8in;
            height: 5.8in;
            background: white;
            color: black;
        }

        .label-container {
            width: 100%;
            height: 100%;
            border: 2px solid #000;
            padding: 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 8px;
            margin-bottom: 8px;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            color: #4D734E;
            margin-bottom: 2px;
        }

        .document-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .order-info {
            font-size: 9px;
            margin-bottom: 8px;
        }

        .customer-section {
            border: 1px solid #000;
            padding: 6px;
            margin-bottom: 8px;
            background: #f8f9fa;
        }

        .customer-section h4 {
            margin: 0 0 4px 0;
            font-size: 10px;
            font-weight: bold;
            color: #4D734E;
        }

        .address {
            line-height: 1.2;
            font-size: 9px;
        }

        .items-section {
            flex: 1;
            border: 1px solid #000;
            padding: 6px;
            margin-bottom: 8px;
        }

        .items-section h4 {
            margin: 0 0 6px 0;
            font-size: 10px;
            font-weight: bold;
            color: #4D734E;
        }

        .item-list {
            font-size: 8px;
            line-height: 1.3;
            max-height: 200px;
            overflow: hidden;
        }

        .item {
            margin-bottom: 4px;
            padding: 3px;
            border-bottom: 1px dotted #ccc;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4px;
        }

        .item:last-child {
            border-bottom: none;
        }

        .item-details {
            font-size: 8px;
        }

        .item-number {
            font-weight: bold;
            color: #4D734E;
        }

        .item-description {
            color: #666;
            font-size: 7px;
            margin-top: 1px;
        }

        .item-qty {
            text-align: right;
            font-weight: bold;
            font-size: 9px;
        }

        .box-summary {
            border: 1px solid #000;
            padding: 4px;
            margin-bottom: 6px;
            background: #e3f2fd;
        }

        .box-summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            font-size: 8px;
        }

        .footer {
            border-top: 1px solid #000;
            padding-top: 4px;
            text-align: center;
            font-size: 8px;
        }

        .barcode-section {
            text-align: center;
            margin: 4px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            letter-spacing: 2px;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        .urgent {
            background: #f8d7da;
            border: 1px solid #dc3545;
            color: #721c24;
            padding: 2px 4px;
            font-size: 8px;
            text-align: center;
            margin-bottom: 4px;
        }

        .no-prices-notice {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 3px;
            font-size: 7px;
            text-align: center;
            margin-bottom: 6px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="label-container">
        <div class="header">
            <div class="company-name">LAMART</div>
            <div class="document-title">PACKING SLIP</div>
            <div class="order-info">
                Order #{{$order->order_number}} | Quote #{{$order->quote_number ?? 'N/A'}}<br>
                {{$order->created_at->format('M d, Y')}}
            </div>
        </div>

        <div class="no-prices-notice">
            📋 CONTENTS ONLY - NO PRICING INFORMATION
        </div>

        @if($order->delivery_method == 'express' || $order->delivery_method == 'urgent')
        <div class="urgent">
            ⚡ {{strtoupper($order->delivery_method)}} DELIVERY
        </div>
        @endif

        <div class="customer-section">
            <h4>📦 CUSTOMER:</h4>
            <div class="address">
                <strong>{{$order->first_name}} {{$order->last_name}}</strong><br>
                @if($order->company_name)<strong>{{$order->company_name}}</strong><br>@endif
                {{$order->address1}}<br>
                @if($order->address2){{$order->address2}}<br>@endif
                {{$order->country}}, {{$order->post_code}}<br>
                📞 {{$order->phone}}
            </div>
        </div>

        <div class="box-summary">
            <div class="box-summary-grid">
                <div><strong>Total Boxes:</strong></div>
                <div>{{$order->boxes->count()}} boxes</div>
                <div><strong>Total Items:</strong></div>
                <div>{{$order->items->sum('packed_quantity') ?: $order->items->sum('quantity')}} items</div>
                <div><strong>Delivery:</strong></div>
                <div>{{ucfirst($order->delivery_method ?? 'Standard')}}</div>
                <div><strong>Weight:</strong></div>
                <div>{{$order->boxes->sum('weight') ?: 'TBD'}} lbs</div>
            </div>
        </div>

        <div class="items-section">
            <h4>📋 PACKED ITEMS:</h4>
            <div class="item-list">
                @foreach($order->items as $item)
                <div class="item">
                    <div class="item-details">
                        <div class="item-number">
                            @if($item->product && $item->product->item_number)
                                {{$item->product->item_number}}
                            @else
                                Item-{{$item->product_id}}
                            @endif
                        </div>
                        <div class="item-description">
                            @if($item->product && $item->product->title)
                                {{$item->product->title}}
                            @else
                                Product ID: {{$item->product_id}}
                            @endif
                            @if($item->color_name && $item->color_name->name)
                                - {{$item->color_name->name}}
                            @elseif($item->color)
                                - Color ID: {{$item->color}}
                            @endif
                        </div>
                        @if($item->box_id)
                        <div style="font-size: 6px; color: #4D734E; margin-top: 1px;">
                            📦 Box: {{$item->orderBox->box_label ?? 'Box ' . $item->box_id}}
                        </div>
                        @endif
                    </div>
                    <div class="item-qty">
                        Qty: {{$item->packed_quantity ?? $item->quantity}}
                    </div>
                </div>
                @endforeach

                @if($order->items->count() == 0)
                <div style="text-align: center; color: #666; font-style: italic;">
                    No items in this order
                </div>
                @endif
            </div>
        </div>

        <div class="barcode-section">
            {{$order->order_number}}
        </div>

        <div class="footer">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 7px;">
                <div>
                    <strong>Packed:</strong> {{now()->format('m/d/Y H:i')}}<br>
                    <strong>Picker:</strong> {{Auth::user()->first_name}} {{Auth::user()->last_name}}
                </div>
                <div>
                    <strong>Delivery Method:</strong><br>
                    {{strtoupper($order->delivery_method ?? 'STANDARD')}}<br>
                    @if($order->delivery_method == 'pickup')
                        <strong>📍 CUSTOMER PICKUP</strong>
                    @elseif($order->delivery_method == 'truck')
                        <strong>🚛 TRUCK DELIVERY</strong>
                    @else
                        <strong>📦 CARRIER SHIPMENT</strong>
                    @endif
                </div>
            </div>
        </div>

        @if($order->notes)
        <div style="border-top: 1px solid #000; padding-top: 2px; font-size: 7px; color: #666;">
            <strong>Notes:</strong> {{$order->notes}}
        </div>
        @endif
    </div>

    <div class="no-print" style="position: fixed; bottom: 10px; right: 10px;">
        <button onclick="window.print()" style="padding: 8px 16px; background: #4D734E; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 5px;">
            🖨️ Print Slip
        </button>
        <button onclick="window.close()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            ✖️ Close
        </button>
    </div>
</body>
</html>
