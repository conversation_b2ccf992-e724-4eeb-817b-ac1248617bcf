<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShipStationService
{
    private $apiKey;
    private $apiSecret;
    private $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.shipstation.api_key');
        $this->apiSecret = config('services.shipstation.api_secret');
        $this->baseUrl = config('services.shipstation.base_url', 'https://ssapi.shipstation.com');
    }

    /**
     * Get shipping rates for multiple boxes with different carriers
     * Each box can have different dimensions and weight
     */
    public function getShippingRatesForBoxes($fromAddress, $toAddress, $boxes, $carriers = ['fedex', 'ups', 'stamps_com'])
    {
        $allBoxRates = [];

        foreach ($boxes as $boxIndex => $box) {
            $boxRates = $this->getShippingRatesForSingleBox($fromAddress, $toAddress, $box, $carriers);

            if (!isset($boxRates['error'])) {
                // Sort rates by total cost (least to most expensive)
                usort($boxRates, function($a, $b) {
                    return $a['total_cost'] <=> $b['total_cost'];
                });

                $allBoxRates['box_' . ($boxIndex + 1)] = [
                    'box_info' => $box,
                    'rates' => $boxRates
                ];
            } else {
                $allBoxRates['box_' . ($boxIndex + 1)] = [
                    'box_info' => $box,
                    'error' => $boxRates['error']
                ];
            }
        }

        return $allBoxRates;
    }

    /**
     * Get shipping rates for a single box from multiple carriers
     */
    public function getShippingRatesForSingleBox($fromAddress, $toAddress, $box, $carriers = ['fedex', 'ups', 'stamps_com'])
    {
        $allRates = [];

        foreach ($carriers as $carrier) {
            try {
                $rates = $this->getCarrierRatesForBox($fromAddress, $toAddress, $box, $carrier);

                if (!isset($rates['error']) && !empty($rates)) {
                    // Add carrier info to each rate
                    foreach ($rates as $rate) {
                        $rate['carrier_code'] = $carrier;
                        $rate['carrier_name'] = $this->getCarrierName($carrier);
                        $allRates[] = $rate;
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error getting rates for carrier {$carrier}: " . $e->getMessage());
                continue;
            }
        }

        return $allRates;
    }

    /**
     * Get rates for a specific carrier and box
     */
    private function getCarrierRatesForBox($fromAddress, $toAddress, $box, $carrierCode)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->post($this->baseUrl . '/shipments/getrates', [
                    'carrierCode' => $carrierCode,
                    'fromPostalCode' => $fromAddress['postal_code'],
                    'toState' => $toAddress['state'],
                    'toPostalCode' => $toAddress['postal_code'],
                    'toCountry' => $toAddress['country'] ?? 'US',
                    'weight' => [
                        'value' => $box['weight'],
                        'units' => $box['weight_units'] ?? 'pounds'
                    ],
                    'dimensions' => [
                        'length' => $box['length'],
                        'width' => $box['width'],
                        'height' => $box['height'],
                        'units' => $box['dimension_units'] ?? 'inches'
                    ],
                    'confirmation' => 'none',
                    'residential' => $toAddress['residential'] ?? true
                ]);

            if ($response->successful()) {
                return $this->formatRatesResponse($response->json());
            }

            Log::error("ShipStation API Error for {$carrierCode}: " . $response->body());
            return ['error' => "Failed to get rates for {$carrierCode}"];

        } catch (\Exception $e) {
            Log::error("ShipStation {$carrierCode} Error: " . $e->getMessage());
            return ['error' => 'Carrier service unavailable'];
        }
    }

    /**
     * Get rates for specific carriers (FedEx, UPS, USPS)
     */
    public function getCarrierRates($fromAddress, $toAddress, $packages, $carriers = ['fedex', 'ups', 'stamps_com'])
    {
        $allRates = [];

        foreach ($carriers as $carrier) {
            $rates = $this->getCarrierSpecificRates($fromAddress, $toAddress, $packages, $carrier);
            if (!isset($rates['error'])) {
                $allRates[$carrier] = $rates;
            }
        }

        return $allRates;
    }

    /**
     * Get rates for a specific carrier
     */
    private function getCarrierSpecificRates($fromAddress, $toAddress, $packages, $carrierCode)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->post($this->baseUrl . '/shipments/getrates', [
                    'carrierCode' => $carrierCode,
                    'fromPostalCode' => $fromAddress['postal_code'],
                    'toState' => $toAddress['state'],
                    'toPostalCode' => $toAddress['postal_code'],
                    'toCountry' => $toAddress['country'] ?? 'US',
                    'weight' => [
                        'value' => $packages['weight'],
                        'units' => 'pounds'
                    ],
                    'dimensions' => [
                        'length' => $packages['length'],
                        'width' => $packages['width'],
                        'height' => $packages['height'],
                        'units' => 'inches'
                    ],
                    'confirmation' => 'none',
                    'residential' => true
                ]);

            if ($response->successful()) {
                return $this->formatRatesResponse($response->json());
            }

            return ['error' => 'Failed to get rates for ' . $carrierCode];

        } catch (\Exception $e) {
            Log::error("ShipStation {$carrierCode} Error: " . $e->getMessage());
            return ['error' => 'Carrier service unavailable'];
        }
    }

    /**
     * Create a shipment
     */
    public function createShipment($orderData, $carrierCode, $serviceCode)
    {
        try {
            $shipmentData = [
                'carrierCode' => $carrierCode,
                'serviceCode' => $serviceCode,
                'packageCode' => 'package',
                'confirmation' => 'none',
                'shipDate' => now()->format('Y-m-d'),
                'weight' => [
                    'value' => $orderData['weight'],
                    'units' => 'pounds'
                ],
                'dimensions' => [
                    'length' => $orderData['length'],
                    'width' => $orderData['width'],
                    'height' => $orderData['height'],
                    'units' => 'inches'
                ],
                'shipFrom' => [
                    'name' => config('app.name'),
                    'company' => config('app.name'),
                    'street1' => config('services.shipstation.from_address.street1'),
                    'city' => config('services.shipstation.from_address.city'),
                    'state' => config('services.shipstation.from_address.state'),
                    'postalCode' => config('services.shipstation.from_address.postal_code'),
                    'country' => config('services.shipstation.from_address.country', 'US'),
                    'phone' => config('services.shipstation.from_address.phone')
                ],
                'shipTo' => [
                    'name' => $orderData['customer_name'],
                    'company' => $orderData['company_name'] ?? '',
                    'street1' => $orderData['address1'],
                    'street2' => $orderData['address2'] ?? '',
                    'city' => $orderData['city'],
                    'state' => $orderData['state'],
                    'postalCode' => $orderData['postal_code'],
                    'country' => $orderData['country'] ?? 'US',
                    'phone' => $orderData['phone'] ?? ''
                ],
                'insuranceOptions' => [
                    'provider' => 'carrier',
                    'insureShipment' => false,
                    'insuredValue' => 0
                ],
                'internationalOptions' => [
                    'contents' => 'merchandise',
                    'nonDelivery' => 'return_to_sender'
                ],
                'advancedOptions' => [
                    'warehouseId' => null,
                    'nonMachinable' => false,
                    'saturdayDelivery' => false,
                    'containsAlcohol' => false
                ]
            ];

            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->post($this->baseUrl . '/shipments/createlabel', $shipmentData);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('ShipStation Create Shipment Error: ' . $response->body());
            return ['error' => 'Failed to create shipment'];

        } catch (\Exception $e) {
            Log::error('ShipStation Create Shipment Exception: ' . $e->getMessage());
            return ['error' => 'Shipment creation failed'];
        }
    }

    /**
     * Format the rates response for easier consumption
     */
    private function formatRatesResponse($response)
    {
        $formattedRates = [];

        foreach ($response as $rate) {
            $formattedRates[] = [
                'carrier_code' => $rate['carrierCode'],
                'carrier_name' => $this->getCarrierName($rate['carrierCode']),
                'service_code' => $rate['serviceCode'],
                'service_name' => $rate['serviceName'],
                'ship_cost' => $rate['shipmentCost'],
                'other_cost' => $rate['otherCost'],
                'total_cost' => $rate['shipmentCost'] + $rate['otherCost'],
                'delivery_days' => $rate['deliveryDays'] ?? null,
                'guaranteed_service' => $rate['guaranteedService'] ?? false
            ];
        }

        // Sort by total cost
        usort($formattedRates, function($a, $b) {
            return $a['total_cost'] <=> $b['total_cost'];
        });

        return $formattedRates;
    }

    /**
     * Get friendly carrier name
     */
    private function getCarrierName($carrierCode)
    {
        $carriers = [
            'fedex' => 'FedEx',
            'ups' => 'UPS',
            'stamps_com' => 'USPS',
            'usps' => 'USPS',
            'dhl_express' => 'DHL Express',
            'ontrac' => 'OnTrac'
        ];

        return $carriers[$carrierCode] ?? ucfirst($carrierCode);
    }

    /**
     * Get cheapest shipping option across all boxes
     */
    public function getCheapestShippingOption($boxRates)
    {
        $cheapestOptions = [];
        $totalCost = 0;

        foreach ($boxRates as $boxKey => $boxData) {
            if (isset($boxData['rates']) && !empty($boxData['rates'])) {
                // Get the cheapest rate for this box (already sorted)
                $cheapestRate = $boxData['rates'][0];
                $cheapestOptions[$boxKey] = $cheapestRate;
                $totalCost += $cheapestRate['total_cost'];
            }
        }

        return [
            'box_options' => $cheapestOptions,
            'total_shipping_cost' => $totalCost,
            'currency' => 'USD'
        ];
    }

    /**
     * Get shipping options grouped by carrier
     */
    public function getShippingOptionsByCarrier($boxRates)
    {
        $carrierOptions = [];

        foreach ($boxRates as $boxKey => $boxData) {
            if (isset($boxData['rates'])) {
                foreach ($boxData['rates'] as $rate) {
                    $carrierCode = $rate['carrier_code'];

                    if (!isset($carrierOptions[$carrierCode])) {
                        $carrierOptions[$carrierCode] = [
                            'carrier_name' => $rate['carrier_name'],
                            'boxes' => [],
                            'total_cost' => 0
                        ];
                    }

                    $carrierOptions[$carrierCode]['boxes'][$boxKey] = $rate;
                    $carrierOptions[$carrierCode]['total_cost'] += $rate['total_cost'];
                }
            }
        }

        // Sort carriers by total cost
        uasort($carrierOptions, function($a, $b) {
            return $a['total_cost'] <=> $b['total_cost'];
        });

        return $carrierOptions;
    }

    /**
     * Validate box dimensions and weight
     */
    public function validateBox($box)
    {
        $required = ['length', 'width', 'height', 'weight'];

        foreach ($required as $field) {
            if (!isset($box[$field]) || !is_numeric($box[$field]) || $box[$field] <= 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * Test API connection
     */
    public function testConnection()
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->get($this->baseUrl . '/carriers');

            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }


    // sample functions

    private function getshippingtracking($orderhd)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/shipments?carrierCode=" . $orderhd->carrier_type . "&trackingNumber=278696492979",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $this->authorization()
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        // dd($response);
    }

     public function getCarriers($auth_key)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/carriers",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $auth_key
            ),
        ));
        $response = curl_exec($curl);
        return json_decode($response, true);
    }



    public function getPackagesAndServices($carrier_type, $type = null)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/carriers/listpackages?carrierCode=" . $carrier_type,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $this->authorization()
            ),
        ));
        $packages = curl_exec($curl);
        $packages = json_decode($packages, true);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/carriers/listservices?carrierCode=" . $carrier_type,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $this->authorization()
            ),
        ));
        $services = curl_exec($curl);
        $services = json_decode($services, true);

        $response = ['packages' => $packages, 'services' => $services];

        if (!empty($type)) {
            return $response;
        } else {
            echo json_encode($response);
            exit;
        }
    }


    /**
     * Get rates for order with multiple boxes (updated method)
     */
    public function getRatesForOrder($orderhd, $carriers = ['fedex', 'ups', 'stamps_com'])
    {
        // Convert order packages to box format
        $boxes = [];
        foreach ($orderhd->packages as $key => $package) {
            $boxes[] = [
                'length' => $package->length ?? 12,
                'width' => $package->width ?? 12,
                'height' => $package->height ?? 6,
                'weight' => $package->weight,
                'weight_units' => $package->weight_type ?? 'pounds',
                'dimension_units' => 'inches'
            ];
        }

        $fromAddress = [
            'postal_code' => '07666',
            'city' => 'Teaneck',
            'state' => 'NJ',
            'country' => 'US'
        ];

        $toAddress = [
            'postal_code' => $orderhd->zip,
            'city' => $orderhd->city,
            'state' => $orderhd->state,
            'country' => 'US',
            'residential' => false
        ];

        return $this->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes, $carriers);
    }

    /**
     * Legacy method - kept for backward compatibility
     */
    public function getrates($orderhd, $type = null)
    {
        $packagesLineItems = [];
        $weight = 0;
        $w_type = "";
        foreach ($orderhd->packages as $key => $package) {
            $weight += $package->weight;
            $w_type = $package->weight_type;
        }

        $body = [
            'carrierCode' => $orderhd->carrier_type,
            'packageCode' => $orderhd->package_type,
            'fromPostalCode' => '07666',
            'toState' => $orderhd->state,
            'toCountry' => 'US',
            'toPostalCode' => $orderhd->zip,
            'toCity' => $orderhd->city,
            'weight' => ['value' => $weight, 'units' =>  $w_type],
            'residential' => false
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/shipments/getrates",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($body),
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $this->authorization(),
                "Content-Type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, true);
        curl_close($curl);

        return $this->formatRatesResponse($response);
    }


    public function getratesAjaxPost(Request $request)
    {

        // dd($request);

        $orderhd = Orderhd::find($request->oid);
        $packagesLineItems = [];
        $weight = 0;
        foreach ($request->order_weight as $key => $w) {
            $weight += $w;
        }

        $body = [
            'carrierCode' => $request->carrier_type,
            'packageCode' => $request->package_type,
            'fromPostalCode' => '07666',
            'toState' => $orderhd->state,
            'toCountry' => 'US',
            'toPostalCode' => $orderhd->zip,
            'toCity' => $orderhd->city,
            'weight' => ['value' => $weight, 'units' => $request->package_weight],
            'residential' => false
        ];

        // dd($body);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/shipments/getrates",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($body),
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $this->authorization(),
                "Content-Type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        echo $response;
        exit;
    }


    public function getratesAjax($package_type, $id, $carrier_code)
    {

        $orderhd = Orderhd::find($id);
        $packagesLineItems = [];
        $weight = 0;
        foreach ($orderhd->packages as $key => $package) {
            $weight += $package->weight;
        }

        $body = [
            'carrierCode' => $carrier_code,
            'packageCode' => $package_type,
            'fromPostalCode' => '07666',
            'toState' => $orderhd->state,
            'toCountry' => 'US',
            'toPostalCode' => $orderhd->zip,
            'toCity' => $orderhd->city,
            'weight' => ['value' => $weight, 'units' => 'pounds'],
            'residential' => false
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://ssapi.shipstation.com/shipments/getrates",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($body),
            CURLOPT_HTTPHEADER => array(
                "Host: ssapi.shipstation.com",
                "Authorization:Basic " . $this->authorization(),
                "Content-Type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        echo $response;
        exit;
    }


    private function fedex_get_shipping_rates($token, $orderhd)
    {
        if (env('PAYMENT_MODE') == 'local') {
            $url = "https://apis-sandbox.fedex.com/rate/v1/rates/quotes";
            $acc_no = '*********';
        } else {
            $url = "https://apis.fedex.com/rate/v1/rates/quotes";
            $acc_no = '*********';
        }

        $packagesLineItems = [];
        foreach ($orderhd->packages as $key => $package) {
            $packagesLineItems[$key]['weight']['units'] = 'LB';
            $packagesLineItems[$key]['weight']['value'] = $package->weight;
        }

        $body = '{
                    "accountNumber": {
                        "value": "' . $acc_no . '"
                    },
                    "packagingType": "' . $orderhd->package_type . '",
                    "rateRequestControlParameters": {
                        "returnTransitTimes": true,
                        "servicesNeededOnRateFailure": true,
                        "variableOptions": "FREIGHT_GUARANTEE",
                        "rateSortOrder": "SERVICENAMETRADITIONAL"
                    },
                    "requestedShipment": {
                    "shipper": {
                        "address": {
                            "city" : "Teaneck",
                            "state": "NJ",
                            "postalCode": "07666",
                            "countryCode": "US"
                        }
                    },
                    "recipient": {
                        "address": {
                            "city" : "' . $orderhd->city . '",
                            "state": "' . $orderhd->state . '",
                            "postalCode": "' . $orderhd->zip . '",
                            "countryCode": "US"
                        }
                    },
                    "pickupType": "USE_SCHEDULED_PICKUP",
                    "rateRequestType": [
                        "ACCOUNT",
                        "LIST"
                    ],
                    "requestedPackageLineItems": ' . json_encode($packagesLineItems) . '
                    }
                }';

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>  $body,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'X-locale: en_US',
                'Authorization: Bearer ' . $token . '',
            ),
        ));
        $response = curl_exec($curl);
        $response = json_decode($response, true);
        // dd($response);

        $rates = array();
        if (empty($response['errors'])) {
            foreach ($response['output']['rateReplyDetails'] as $key => $shippment) {
                $rates[$key]['serviceType'] = $shippment['serviceType'];
                $rates[$key]['serviceName'] = $shippment['serviceName'];
                $rates[$key]['dayOfWeek'] = $shippment['commit']['dateDetail']['dayOfWeek'];
                $rates[$key]['dayFormat'] = $shippment['commit']['dateDetail']['dayFormat'];

                foreach ($shippment['ratedShipmentDetails'] as $key2 => $shippmentRate) {

                    $rates[$key][$shippmentRate['rateType']]['type'] = $shippmentRate['rateType'];
                    $rates[$key][$shippmentRate['rateType']]['totalBaseCharge'] = $shippmentRate['totalBaseCharge'];
                    $rates[$key][$shippmentRate['rateType']]['totalNetCharge'] = $shippmentRate['totalNetCharge'];
                    $rates[$key][$shippmentRate['rateType']]['totalNetFedExCharge'] = $shippmentRate['totalNetFedExCharge'];
                    $rates[$key][$shippmentRate['rateType']]['totalSurcharges'] = $shippmentRate['shipmentRateDetail']['totalSurcharges'];
                }
            }
            $response = ['response' => 'ok', 'rates' => $rates, 'error' => ''];
        } else {
            $response = ['response' => 'failed', 'rates' => [], 'error' => $response['errors'][0]['message']];
        }

        return $response;
    }

    private function fedex_get_services_packages($token, $orderhd)
    {

        if (env('PAYMENT_MODE') == 'local') {
            $url = "https://apis-sandbox.fedex.com/availability/v1/packageandserviceoptions";
            $acc_no = '*********';
        } else {
            $url = "https://apis.fedex.com/availability/v1/packageandserviceoptions";
            $acc_no = '*********';
        }
        $body = '{
                    "requestedShipment": {
                        "shipper": {
                            "address": {
                                "city" : "Teaneck",
                                "postalCode": "07666",
                                "countryCode": "US"
                            }
                        },
                        "recipients": [{
                            "address": {
                                "city" : "' . $orderhd->city . '",
                                "postalCode": "' . $orderhd->zip . '",
                                "countryCode": "US"
                            }
                        }]
                    },
                    "carrierCodes": [
                        "FDXE",
                        "FDXG"
                    ],
                    "accountNumber": {
                        "value": "' . $acc_no . '"
                    }
                }';

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>  $body,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'X-locale: en_US',
                'Authorization: Bearer ' . $token . '',
            ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, true);
        $service_types = [];
        $package_types = [];
        $maxWeightAllowed = [];
        $i = 0;
        foreach ($response['output']['packageOptions'] as $key => $output) {
            if (!$this->is_in_array($service_types, 'key', $output['serviceType']['key'])) {
                $service_types[$i]['key'] = $output['serviceType']['key'];
                $service_types[$i]['value'] = $output['serviceType']['displayText'];
                $i++;
            }

            if (!$this->is_in_array($package_types, 'key', $output['packageType']['key'])) {
                $package_types[$i]['key'] = $output['packageType']['key'];
                $package_types[$i]['value'] = $output['packageType']['displayText'];

                $maxWeightAllowed[$i]['package_type'] = $output['packageType']['key'];
                $maxWeightAllowed[$i]['weight'] = $output['maxWeightAllowed']['value'];

                $i++;
            }
        }

        return json_encode(['serviceTypes' => $service_types, 'packageTypes' => $package_types, 'max_weight' => $maxWeightAllowed]);
    }

    /**
     * Generate authorization header for legacy methods
     */
    private function authorization()
    {
        return base64_encode($this->apiKey . ':' . $this->apiSecret);
    }

    /**
     * Helper method to check if value exists in array
     */
    private function is_in_array($array, $key, $val)
    {
        foreach ($array as $item) {
            if (isset($item[$key]) && $item[$key] == $val) {
                return true;
            }
        }
        return false;
    }
}
