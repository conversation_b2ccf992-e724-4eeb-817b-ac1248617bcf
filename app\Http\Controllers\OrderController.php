<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Shipping;
use App\Models\User;
use App\Models\Product;
use PDF;
use Notification;
use Helper;
use Illuminate\Support\Str;
use App\Notifications\StatusNotification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Events\OrderStatusChanged;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $orders=Order::orderBy('id','DESC')->paginate(10);
        return view('backend.order.index')->with('orders',$orders);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $customers = User::where('role', 'user')
            ->with(['shippingPreference', 'paymentPreference'])
            ->get();

        $products = Product::where('status', 'active')->get();

        $previousOrders = Order::where('user_id', 31)
            ->with('items.product')
            ->latest()
            ->take(10)
            ->get();

        return view('backend.order.create', compact('customers', 'products', 'previousOrders'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $baseRules = [
                'first_name'       => 'required|string',
                'last_name'        => 'required|string',
                'address1'         => 'required|string',
                'phone'            => 'required|string',
                'post_code'        => 'nullable|string',
                'email'            => 'required|email',

                'order_type'       => 'required|in:regular,quick',
                'payment_method'   => 'required|in:cod,paypal,credit_card,bank_transfer,check',
                'shipping_method'  => 'required|in:ship,delivery,pickup',
            ];

            if ($request->payment_method === 'check') {
                $baseRules = array_merge($baseRules, [
                    'check_number'   => 'required|string',
                    'check_front'    => 'required|image|mimes:jpeg,png,jpg|max:2048',
                    'check_back'     => 'required|image|mimes:jpeg,png,jpg|max:2048',
                    'check_date'     => 'required|date',
                    'bank_name'      => 'required|string',
                ]);
            }

            if ($request->order_type === 'quick') {
                $baseRules = array_merge($baseRules, [
                    'previous_order_ids'   => 'required|array',
                    'previous_order_ids.*' => 'exists:orders,id',
                ]);
            }

            $this->validate($request, $baseRules);
        } catch(\Exception $e){
            dd($e);
        }

        try {
            DB::beginTransaction();

            // Prepare order data
            $orderData = [
                'order_number' => 'ORD-' . strtoupper(Str::random(10)),
                'quote_number' => Order::generateQuoteNumber(),
                'user_id' => 13,
                'is_draft' => $request->has('save_as_draft'),
                'is_quick_order' => $request->order_type == 'quick',
                'is_instant_order' => $request->order_type == 'instant',
                'original_order_id' => $request->order_type == 'quick' ? $request->original_order_id : null,
                'delivery_instructions' => $request->delivery_instructions,
                'sub_total' => Helper::totalCartPrice(),
                'quantity' => Helper::cartCount(),
                'shipping_id' => $request->shipping_id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'country' => $request->country,
                'post_code' => $request->post_code,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'status' => $request->has('save_as_draft') ? 'draft' : 'new',
                'payment_method' => $request->payment_method,
                'payment_status' => $request->payment_method == 'paypal' ? 'paid' : 'unpaid',
            ];

            // Apply coupon if exists
            if(session('coupon')) {
                $orderData['coupon'] = session('coupon')['value'];
            }

            // Calculate shipping and totals
            $shipping = Shipping::findOrFail($request->shipping_id);
            $orderData['shipping_charge'] = $shipping->price;
            $orderData['total_amount'] = $this->calculateTotalAmount(
                $orderData['sub_total'],
                $orderData['shipping_charge'],
                $orderData['coupon'] ?? 0
            );

            // Handle check transfer payment
            if ($request->payment_method == 'check') {
                $checkData = $this->processCheckTransfer($request);
                $order = Order::create(array_merge($orderData, $checkData));

                // Create check transfer record
                CheckTransfer::create([
                    'order_id' => $order->id,
                    'check_number' => $request->check_number,
                    'check_date' => $request->check_date,
                    'bank_name' => $request->bank_name,
                    'front_image' => $checkData['check_front'],
                    'back_image' => $checkData['check_back'],
                    'status' => 'pending_verification'
                ]);
            } else {
                $order = Order::create($orderData);
            }

            // Process order items based on order type
            if ($request->order_type == 'regular') {
                $this->processRegularOrder($order);
            } elseif ($request->order_type == 'quick') {
                $this->processQuickOrder($order, $request->original_order_id);
            }

            // Save shipping preference if requested
            if ($request->save_shipping_pref) {
                ShippingPreference::updateOrCreate(
                    ['user_id' => 13],
                    [
                        'shipping_method_id' => $request->shipping_id,
                        'preferred_method' => $shipping->type
                    ]
                );
            }

            // Update cart with order ID
            Cart::where('user_id', 13)
                ->where('order_id', null)
                ->update(['order_id' => $order->id]);

            // Clear session data
            session()->forget('cart');
            session()->forget('coupon');

            // Notify admin
            $this->notifyAdmin($order);

            DB::commit();

            // Redirect based on payment method
            if ($request->payment_method == 'paypal') {
                return redirect()->route('payment')->with(['id' => $order->id]);
            }

            request()->session()->flash('success', 'Order placed successfully!');
            return redirect()->route('home');

        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            request()->session()->flash('error', 'Error creating order: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    private function calculateTotalAmount($subtotal, $shipping, $coupon = 0)
    {
        $total = $subtotal + $shipping;
        if ($coupon) {
            $total -= $coupon;
        }
        return max($total, 0);
    }

    private function processCheckTransfer($request)
    {
        $frontPath = $this->uploadCheckImage($request->file('check_front'));
        $backPath = $this->uploadCheckImage($request->file('check_back'));

        return [
            'check_number' => $request->check_number,
            'check_front' => $frontPath,
            'check_back' => $backPath,
            'check_date' => $request->check_date,
            'bank_name' => $request->bank_name,
        ];
    }

    private function uploadCheckImage($file)
    {
        $filename = 'checks/' . Str::uuid() . '.' . $file->getClientOriginalExtension();
        Storage::disk('public')->put($filename, file_get_contents($file));
        return $filename;
    }

    private function processRegularOrder($order)
    {
        $carts = Cart::where('user_id', auth()->user()->id)
                    ->where('order_id', null)
                    ->get();

        foreach ($carts as $cart) {
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $cart->product_id,
                'quantity' => $cart->quantity,
                'price' => $cart->price,
                'amount' => $cart->amount,
                'status' => 'pending',
            ]);
        }
    }

    private function processQuickOrder($order, $originalOrderId)
    {
        $originalOrder = Order::with('items')->findOrFail($originalOrderId);

        foreach ($originalOrder->items as $item) {
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->price,
                'amount' => $item->amount,
                'status' => 'pending',
            ]);
        }
    }

    private function notifyAdmin($order)
    {
        $admins = User::whereHas('role', function($q) {
            $q->where('slug', 'admin');
        })->get();

        $details = [
            'title' => 'New order created',
            'actionURL' => route('order.show', $order->id),
            'fas' => 'fa-file-alt'
        ];

        Notification::send($admins, new StatusNotification($details));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $order=Order::find($id);
        // return $order;
        return view('backend.order.show')->with('order',$order);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $order=Order::find($id);
        return view('backend.order.edit')->with('order',$order);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $order=Order::find($id);
        $this->validate($request,[
            'status'=>'required|in:new,process,delivered,cancel'
        ]);

        $oldStatus = $order->status; // Store old status for event
        $data=$request->all();

        // return $request->status;
        if($request->status=='delivered'){
            foreach($order->cart as $cart){
                $product=$cart->product;
                // Note: Stock is now managed at item_colors level, not product level
                // This logic may need to be updated to handle specific item colors
                // For now, we'll skip the stock update as it's handled elsewhere
            }
        }

        $status=$order->fill($data)->save();

        if($status){
            // Dispatch order status changed event
            event(new OrderStatusChanged($order, $oldStatus, $request->status));

            request()->session()->flash('success','Successfully updated order');
        }
        else{
            request()->session()->flash('error','Error while updating order');
        }
        return redirect()->route('order.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $order=Order::find($id);
        if($order){
            $status=$order->delete();
            if($status){
                request()->session()->flash('success','Order Successfully deleted');
            }
            else{
                request()->session()->flash('error','Order can not deleted');
            }
            return redirect()->route('order.index');
        }
        else{
            request()->session()->flash('error','Order can not found');
            return redirect()->back();
        }
    }

    public function orderTrack(){
        return view('frontend.pages.order-track');
    }

    public function productTrackOrder(Request $request){
        // return $request->all();
        $order=Order::where('user_id',auth()->user()->id)->where('order_number',$request->order_number)->first();
        if($order){
            if($order->status=="new"){
            request()->session()->flash('success','Your order has been placed. please wait.');
            return redirect()->route('home');

            }
            elseif($order->status=="process"){
                request()->session()->flash('success','Your order is under processing please wait.');
                return redirect()->route('home');

            }
            elseif($order->status=="delivered"){
                request()->session()->flash('success','Your order is successfully delivered.');
                return redirect()->route('home');

            }
            else{
                request()->session()->flash('error','Your order canceled. please try again');
                return redirect()->route('home');

            }
        }
        else{
            request()->session()->flash('error','Invalid order numer please try again');
            return back();
        }
    }

    // PDF generate
    public function pdf(Request $request){
        $order=Order::getAllOrder($request->id);
        // return $order;
        $file_name=$order->order_number.'-'.$order->first_name.'.pdf';
        // return $file_name;
        $pdf=PDF::loadview('backend.order.pdf',compact('order'));
        return $pdf->download($file_name);
    }
    // Income chart
    public function incomeChart(Request $request){
        $year=\Carbon\Carbon::now()->year;
        // dd($year);
        $items=Order::with(['cart_info'])->whereYear('created_at',$year)->where('status','delivered')->get()
            ->groupBy(function($d){
                return \Carbon\Carbon::parse($d->created_at)->format('m');
            });
            // dd($items);
        $result=[];
        foreach($items as $month=>$item_collections){
            foreach($item_collections as $item){
                $amount=$item->cart_info->sum('amount');
                // dd($amount);
                $m=intval($month);
                // return $m;
                isset($result[$m]) ? $result[$m] += $amount :$result[$m]=$amount;
            }
        }
        $data=[];
        for($i=1; $i <=12; $i++){
            $monthName=date('F', mktime(0,0,0,$i,1));
            $data[$monthName] = (!empty($result[$i]))? number_format((float)($result[$i]), 2, '.', '') : 0.0;
        }
        return $data;
    }
}
