<?php

namespace App\Events;

use App\Models\Order;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderAssignedToPicker implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    public $picker;
    public $assignedBy;

    /**
     * Create a new event instance.
     *
     * @param Order $order
     * @param User $picker
     * @param User $assignedBy
     * @return void
     */
    public function __construct(Order $order, User $picker, User $assignedBy)
    {
        $this->order = $order;
        $this->picker = $picker;
        $this->assignedBy = $assignedBy;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return [
            new PrivateChannel('picker.' . $this->picker->id),
            new Channel('warehouse-notifications')
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'customer_name' => $this->order->first_name . ' ' . $this->order->last_name,
            'total_amount' => $this->order->total_amount,
            'item_count' => $this->order->cart_info->count(),
            'assigned_by' => $this->assignedBy->first_name . ' ' . $this->assignedBy->last_name,
            'assigned_at' => now()->toISOString(),
            'picker_name' => $this->picker->first_name . ' ' . $this->picker->last_name,
            'message' => "New order #{$this->order->order_number} has been assigned to you"
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'order.assigned.picker';
    }
}
