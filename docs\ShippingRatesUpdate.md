# ShipStation Service - Box-Based Shipping Rates Update

## Overview

The ShipStationService has been updated to handle shipping rates for multiple boxes with individual dimensions and weights. The service now provides rates from different carriers (FedEx, UPS, USPS) sorted from least to most expensive for each box separately.

## Key Features

### 1. Individual Box Processing
- Each box is processed separately with its own dimensions and weight
- Supports different carriers for each box
- Returns rates sorted by cost (cheapest first) for each box

### 2. Multiple Carrier Support
- FedEx (`fedex`)
- UPS (`ups`) 
- USPS (`stamps_com`)
- Configurable carrier list

### 3. Cost Analysis Tools
- Find cheapest shipping option across all boxes
- Group shipping options by carrier
- Compare total shipping costs

## Updated Methods

### Core Methods

#### `getShippingRatesForBoxes($fromAddress, $toAddress, $boxes, $carriers)`
Gets shipping rates for multiple boxes from different carriers.

**Parameters:**
- `$fromAddress`: Array with postal_code, city, state, country
- `$toAddress`: Array with postal_code, city, state, country, residential
- `$boxes`: Array of boxes with dimensions and weight
- `$carriers`: Array of carrier codes (default: ['fedex', 'ups', 'stamps_com'])

**Returns:**
```php
[
    'box_1' => [
        'box_info' => [...],
        'rates' => [
            [
                'carrier_code' => 'fedex',
                'carrier_name' => 'FedEx',
                'service_code' => 'fedex_ground',
                'service_name' => 'FedEx Ground',
                'total_cost' => 12.50,
                'delivery_days' => 3
            ],
            // ... more rates sorted by cost
        ]
    ],
    'box_2' => [...]
]
```

#### `getCheapestShippingOption($boxRates)`
Finds the cheapest shipping option for each box.

**Returns:**
```php
[
    'box_options' => [
        'box_1' => [...cheapest rate...],
        'box_2' => [...cheapest rate...]
    ],
    'total_shipping_cost' => 25.75,
    'currency' => 'USD'
]
```

#### `getShippingOptionsByCarrier($boxRates)`
Groups shipping options by carrier with total costs.

**Returns:**
```php
[
    'fedex' => [
        'carrier_name' => 'FedEx',
        'boxes' => [
            'box_1' => [...rate...],
            'box_2' => [...rate...]
        ],
        'total_cost' => 28.50
    ],
    // ... other carriers sorted by total cost
]
```

### Helper Methods

#### `validateBox($box)`
Validates box data before processing.

#### `getRatesForOrder($orderhd, $carriers)`
Converts order packages to box format and gets rates.

## Controller Updates

### ShippingController New Methods

#### `getShippingRates($request, $orderId)`
Updated to handle individual boxes with comprehensive rate analysis.

#### `getBoxShippingRates($request)`
New endpoint for getting rates for custom box configurations.

**Request Format:**
```json
{
    "boxes": [
        {
            "weight": 5.5,
            "length": 12,
            "width": 10,
            "height": 8
        },
        {
            "weight": 3.2,
            "length": 15,
            "width": 12,
            "height": 6
        }
    ],
    "to_state": "CA",
    "to_postal_code": "90210",
    "to_city": "Beverly Hills",
    "residential": true
}
```

## API Endpoints

### New Routes
- `POST /admin/shipping/box-rates` - Get rates for custom box configurations
- `GET /admin/shipping/order-rates/{orderId}` - Show rates interface for specific order

### Updated Routes
- `GET /admin/shipping/rates/{orderId}` - Now returns box-based rates with cost analysis

## Usage Examples

### Basic Usage
```php
use App\Services\ShipStationService;

$service = new ShipStationService();

$fromAddress = [
    'postal_code' => '07666',
    'city' => 'Teaneck',
    'state' => 'NJ',
    'country' => 'US'
];

$toAddress = [
    'postal_code' => '90210',
    'city' => 'Beverly Hills',
    'state' => 'CA',
    'country' => 'US',
    'residential' => true
];

$boxes = [
    [
        'length' => 12,
        'width' => 10,
        'height' => 8,
        'weight' => 5.5,
        'weight_units' => 'pounds',
        'dimension_units' => 'inches'
    ]
];

// Get rates for all boxes
$boxRates = $service->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);

// Get cheapest option
$cheapest = $service->getCheapestShippingOption($boxRates);

// Get options by carrier
$byCarrier = $service->getShippingOptionsByCarrier($boxRates);
```

### With Laravel Order Model
```php
// In your controller
public function getOrderShippingRates($orderId)
{
    $order = Order::with('boxes')->findOrFail($orderId);
    $service = new ShipStationService();
    
    $rates = $service->getRatesForOrder($order);
    
    return response()->json([
        'box_rates' => $rates,
        'cheapest' => $service->getCheapestShippingOption($rates)
    ]);
}
```

## Configuration

Ensure your `.env` file has the required ShipStation configuration:

```env
SHIPSTATION_API_KEY=your_api_key
SHIPSTATION_API_SECRET=your_api_secret
SHIPSTATION_BASE_URL=https://ssapi.shipstation.com
SHIPSTATION_FROM_STREET1=123 Main St
SHIPSTATION_FROM_CITY=Your City
SHIPSTATION_FROM_STATE=CA
SHIPSTATION_FROM_POSTAL_CODE=12345
SHIPSTATION_FROM_COUNTRY=US
SHIPSTATION_FROM_PHONE=************
```

## Benefits

1. **Accurate Pricing**: Each box gets individual rates based on actual dimensions and weight
2. **Cost Optimization**: Easy identification of cheapest shipping options
3. **Carrier Comparison**: Compare rates across different carriers
4. **Flexible Configuration**: Support for different box configurations
5. **Sorted Results**: Rates automatically sorted from least to most expensive
6. **Error Handling**: Robust error handling for API failures

## Migration Notes

- Legacy methods are maintained for backward compatibility
- New methods use Laravel HTTP client instead of cURL for better error handling
- Box validation ensures data integrity before API calls
- Comprehensive logging for debugging

## Testing

Use the example file `examples/ShipStationServiceUsage.php` to test the functionality with sample data.
