@extends('backend.emails.layout')

@section('content')
<div class="email-header">
    <h2 style="color: #4D734E; margin-bottom: 20px;">
        <i class="fas fa-check-circle"></i> Your Order Has Been Completed
    </h2>
</div>

<div class="order-info">
    <div class="info-grid">
        <div class="info-item">
            <strong>Order Number:</strong>
            <span>{{ $order->order_number }}</span>
        </div>
        <div class="info-item">
            <strong>Quote Number:</strong>
            <span>{{ $order->quote_number }}</span>
        </div>
        <div class="info-item">
            <strong>Customer:</strong>
            <span>{{ $order->first_name }} {{ $order->last_name }}</span>
        </div>
        @if($order->company_name)
        <div class="info-item">
            <strong>Company:</strong>
            <span>{{ $order->company_name }}</span>
        </div>
        @endif
        <div class="info-item">
            <strong>Completed By:</strong>
            <span>{{ $completed_by->first_name }} {{ $completed_by->last_name }}</span>
        </div>
        <div class="info-item">
            <strong>Completed At:</strong>
            <span>{{ now()->format('M d, Y g:i A') }}</span>
        </div>
    </div>
</div>

<div class="completion-summary">
    <h3 style="color: #4D734E; border-bottom: 2px solid #4D734E; padding-bottom: 10px;">
        📦 Order Summary
    </h3>
    
    <div class="summary-grid">
        <div class="summary-item">
            <div class="summary-label">Total Boxes:</div>
            <div class="summary-value">{{ $total_boxes }}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">Packed Items:</div>
            <div class="summary-value">{{ $total_packed_items }}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">Order Value:</div>
            <div class="summary-value">${{ number_format($order->total_amount, 2) }}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">Delivery Method:</div>
            <div class="summary-value">{{ ucfirst($order->delivery_method ?? 'Standard') }}</div>
        </div>
    </div>
</div>

@if($has_backorders)
<div class="alert alert-info">
    <h4><i class="fas fa-info-circle"></i> Backorders Notice</h4>
    <p>Some items from this order were not available and have been moved to backorders. You can review and manage these backorders in your dashboard.</p>
</div>
@endif

<div class="next-steps">
    <h3 style="color: #4D734E; border-bottom: 2px solid #4D734E; padding-bottom: 10px;">
        📋 What's Next
    </h3>
    <ul>
        <li>The office will create an invoice based on packed quantities</li>
        <li>You'll be notified when the order is ready for delivery/shipping</li>
        @if($has_backorders)
        <li>Review backorders for this customer and consider combining with future orders</li>
        @endif
        <li>Follow up with customer about delivery arrangements</li>
    </ul>
</div>

<div class="customer-info">
    <h3 style="color: #4D734E; border-bottom: 2px solid #4D734E; padding-bottom: 10px;">
        👤 Customer Information
    </h3>
    <div class="customer-details">
        <p><strong>Name:</strong> {{ $order->first_name }} {{ $order->last_name }}</p>
        @if($order->company_name)
        <p><strong>Company:</strong> {{ $order->company_name }}</p>
        @endif
        <p><strong>Email:</strong> {{ $order->email }}</p>
        @if($order->phone)
        <p><strong>Phone:</strong> {{ $order->phone }}</p>
        @endif
        @if($order->address1)
        <p><strong>Address:</strong> 
            {{ $order->address1 }}
            @if($order->address2), {{ $order->address2 }}@endif
            <br>{{ $order->city }}, {{ $order->state }} {{ $order->post_code }}
        </p>
        @endif
    </div>
</div>

<div class="action-buttons">
    <a href="{{ url('/salesman/orders/' . $order->id) }}" class="btn btn-primary">
        View Order Details
    </a>
    @if($has_backorders)
    <a href="{{ url('/salesman/customers/' . $order->user_id . '/backorders') }}" class="btn btn-warning">
        View Customer Backorders
    </a>
    @endif
</div>

<style>
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.summary-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #4D734E;
}

.summary-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
}

.alert-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.next-steps ul {
    padding-left: 20px;
}

.next-steps li {
    margin: 8px 0;
}

.customer-details p {
    margin: 8px 0;
}

.action-buttons {
    margin: 30px 0;
    text-align: center;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 0 10px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}
</style>
@endsection
