<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packing List 4x6 - Order #{{$order->order_number}}</title>
    <style>
        /* 4x6 inch format (288pt x 432pt at 72dpi) */
        @page {
            size: 4in 6in;
            margin: 0.1in;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 8px;
            line-height: 1.1;
            margin: 0;
            padding: 2px;
            background: white;
            color: black;
            width: 3.8in;
            height: 5.8in;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px solid #000;
            padding-bottom: 2px;
            margin-bottom: 3px;
        }
        
        .company-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 1px;
        }
        
        .document-title {
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 1px;
        }
        
        .order-header {
            font-size: 7px;
            margin-bottom: 2px;
        }
        
        .info-section {
            margin-bottom: 3px;
            font-size: 7px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
        }
        
        .customer-info {
            border: 1px solid #000;
            padding: 2px;
            margin-bottom: 3px;
            font-size: 7px;
        }
        
        .customer-name {
            font-weight: bold;
            font-size: 8px;
            margin-bottom: 1px;
        }
        
        .items-section {
            border: 1px solid #000;
            margin-bottom: 3px;
        }
        
        .items-header {
            background: #000;
            color: white;
            padding: 1px 2px;
            font-size: 7px;
            font-weight: bold;
            text-align: center;
        }
        
        .item-row {
            padding: 1px 2px;
            border-bottom: 1px dotted #ccc;
            font-size: 6px;
            display: flex;
            justify-content: space-between;
        }
        
        .item-row:last-child {
            border-bottom: none;
        }
        
        .item-number {
            font-weight: bold;
            width: 25%;
        }
        
        .item-desc {
            width: 50%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .item-qty {
            width: 15%;
            text-align: center;
            font-weight: bold;
        }
        
        .item-status {
            width: 10%;
            text-align: center;
        }
        
        .footer {
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            font-size: 6px;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 2px;
        }
        
        .signature-box {
            border: 1px solid #000;
            height: 15px;
            margin: 2px 0;
            position: relative;
        }
        
        .signature-label {
            position: absolute;
            top: -8px;
            left: 2px;
            background: white;
            padding: 0 2px;
            font-size: 6px;
        }
        
        .barcode-section {
            text-align: center;
            margin: 2px 0;
        }
        
        .barcode {
            font-family: 'Libre Barcode 128', monospace;
            font-size: 16px;
            letter-spacing: 0;
        }
        
        /* Print optimizations for thermal printers */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        /* Zebra printer specific optimizations */
        .zebra-optimized {
            font-family: 'Courier New', monospace;
            font-weight: normal;
        }
        
        .bold {
            font-weight: bold;
        }
    </style>
</head>
<body class="zebra-optimized">
    <div class="header">
        <div class="company-name bold">LAMART</div>
        <div class="document-title">PACKING LIST</div>
        <div class="order-header">{{$order->order_number}} | {{$order->quote_number}}</div>
        <div class="order-header">{{$order->created_at->format('m/d/Y H:i')}}</div>
    </div>

    <div class="info-section">
        <div class="info-row">
            <span><strong>Items:</strong> {{$order->cart_info->count()}}</span>
            <span><strong>Total:</strong> ${{number_format($order->total_amount,2)}}</span>
        </div>
        <div class="info-row">
            <span><strong>Method:</strong> {{ucfirst($order->delivery_method ?? 'N/A')}}</span>
            <span><strong>Status:</strong> {{ucfirst($order->status)}}</span>
        </div>
    </div>

    <div class="customer-info">
        <div class="customer-name">{{$order->first_name}} {{$order->last_name}}</div>
        <div>{{$order->email}}</div>
        <div>{{$order->phone}}</div>
        <div>{{$order->address1}}</div>
        @if($order->address2)<div>{{$order->address2}}</div>@endif
        <div>{{$order->country}}, {{$order->post_code}}</div>
    </div>

    <div class="items-section">
        <div class="items-header">ITEMS TO PACK</div>
        @foreach($order->cart_info as $item)
        <div class="item-row">
            <div class="item-number bold">{{$item->product->item_number ?? 'N/A'}}</div>
            <div class="item-desc">{{Str::limit($item->product->title, 20)}}</div>
            <div class="item-qty bold">{{$item->quantity}}</div>
            <div class="item-status">[ ]</div>
        </div>
        @endforeach
    </div>

    @if($order->delivery_instructions)
    <div style="font-size: 6px; margin: 2px 0; border: 1px solid #000; padding: 2px;">
        <strong>Instructions:</strong> {{Str::limit($order->delivery_instructions, 80)}}
    </div>
    @endif

    <div class="barcode-section">
        <div class="barcode">{{$order->order_number}}</div>
    </div>

    <div class="footer">
        <div class="signature-box">
            <div class="signature-label">Picker Signature</div>
        </div>
        <div style="margin-top: 2px;">
            Picker: {{Auth::user()->first_name}} {{Auth::user()->last_name}} | {{now()->format('m/d/Y H:i')}}
        </div>
    </div>

    <!-- Print button for testing (hidden in print) -->
    <div class="no-print" style="position: fixed; top: 10px; right: 10px; z-index: 1000;">
        <button onclick="window.print()" style="padding: 10px; background: #4D734E; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Print 4x6 Label
        </button>
    </div>
</body>
</html>
