<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * Display a listing of invoices
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['order', 'createdBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        // Search by customer or invoice number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        $invoices = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('backend.invoices.index', compact('invoices'));
    }

    /**
     * Show the form for creating a new invoice from an order
     */
    public function createFromOrder($orderId)
    {
        $order = Order::with(['cart_info.product', 'boxes.items.product', 'boxes.items.color_name'])
                     ->findOrFail($orderId);

        // Check if order is completed
        if ($order->status !== 'shipped') {
            return redirect()->back()->with('error', 'Can only create invoices for completed orders.');
        }

        // Check if invoice already exists
        if ($order->invoice) {
            return redirect()->route('admin.invoices.show', $order->invoice->id)
                           ->with('info', 'Invoice already exists for this order.');
        }

        return view('backend.invoices.create', compact('order'));
    }

    /**
     * Store a newly created invoice
     */
    public function store(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after:invoice_date',
            'tax_amount' => 'nullable|numeric|min:0',
            'shipping_cost' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'terms' => 'nullable|string',
            'items' => 'required|array',
            'items.*.invoiced_quantity' => 'required|integer|min:0',
            'items.*.unit_price' => 'required|numeric|min:0'
        ]);

        try {
            DB::beginTransaction();

            $order = Order::findOrFail($request->order_id);

            // Create invoice
            $invoice = Invoice::create([
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'order_id' => $order->id,
                'created_by' => Auth::id(),
                'status' => 'draft',
                'customer_name' => $order->first_name . ' ' . $order->last_name,
                'customer_email' => $order->email,
                'customer_phone' => $order->phone,
                'company_name' => $order->company_name,
                'billing_address' => $order->getFullAddress(),
                'shipping_address' => $order->getFullShippingAddress(),
                'subtotal' => 0, // Will be calculated
                'tax_amount' => $request->tax_amount ?? 0,
                'shipping_cost' => $request->shipping_cost ?? $order->shipping_cost ?? 0,
                'discount_amount' => $request->discount_amount ?? 0,
                'total_amount' => 0, // Will be calculated
                'invoice_date' => $request->invoice_date,
                'due_date' => $request->due_date,
                'notes' => $request->notes,
                'terms' => $request->terms ?? 'Payment due within 30 days'
            ]);

            // Create invoice items
            $subtotal = 0;
            foreach ($request->items as $itemData) {
                $lineTotal = $itemData['invoiced_quantity'] * $itemData['unit_price'];
                $subtotal += $lineTotal;

                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $itemData['product_id'],
                    'color_id' => $itemData['color_id'] ?? null,
                    'item_number' => $itemData['item_number'] ?? null,
                    'product_name' => $itemData['product_name'],
                    'color_name' => $itemData['color_name'] ?? null,
                    'description' => $itemData['description'] ?? null,
                    'original_quantity' => $itemData['original_quantity'],
                    'packed_quantity' => $itemData['packed_quantity'],
                    'invoiced_quantity' => $itemData['invoiced_quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'line_total' => $lineTotal,
                    'cart_item_id' => $itemData['cart_item_id'] ?? null,
                    'order_box_item_id' => $itemData['order_box_item_id'] ?? null
                ]);
            }

            // Update invoice totals
            $totalAmount = $subtotal + $invoice->tax_amount + $invoice->shipping_cost - $invoice->discount_amount;
            $invoice->update([
                'subtotal' => $subtotal,
                'total_amount' => $totalAmount
            ]);

            DB::commit();

            return redirect()->route('admin.invoices.show', $invoice->id)
                           ->with('success', 'Invoice created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                           ->with('error', 'Error creating invoice: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified invoice
     */
    public function show($id)
    {
        $invoice = Invoice::with(['order', 'items.product', 'items.color', 'createdBy'])
                         ->findOrFail($id);

        return view('backend.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice
     */
    public function edit($id)
    {
        $invoice = Invoice::with(['order', 'items.product', 'items.color'])
                         ->findOrFail($id);

        // Only allow editing of draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('admin.invoices.show', $id)
                           ->with('error', 'Only draft invoices can be edited.');
        }

        return view('backend.invoices.edit', compact('invoice'));
    }

    /**
     * Update the specified invoice
     */
    public function update(Request $request, $id)
    {
        $invoice = Invoice::findOrFail($id);

        // Only allow editing of draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('admin.invoices.show', $id)
                           ->with('error', 'Only draft invoices can be edited.');
        }

        $request->validate([
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after:invoice_date',
            'tax_amount' => 'nullable|numeric|min:0',
            'shipping_cost' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'terms' => 'nullable|string',
            'items' => 'required|array',
            'items.*.invoiced_quantity' => 'required|integer|min:0',
            'items.*.unit_price' => 'required|numeric|min:0'
        ]);

        try {
            DB::beginTransaction();

            // Update invoice details
            $invoice->update([
                'invoice_date' => $request->invoice_date,
                'due_date' => $request->due_date,
                'tax_amount' => $request->tax_amount ?? 0,
                'shipping_cost' => $request->shipping_cost ?? 0,
                'discount_amount' => $request->discount_amount ?? 0,
                'notes' => $request->notes,
                'terms' => $request->terms
            ]);

            // Update invoice items
            $subtotal = 0;
            foreach ($request->items as $itemId => $itemData) {
                $item = InvoiceItem::findOrFail($itemId);
                $lineTotal = $itemData['invoiced_quantity'] * $itemData['unit_price'];
                $subtotal += $lineTotal;

                $item->update([
                    'invoiced_quantity' => $itemData['invoiced_quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'line_total' => $lineTotal
                ]);
            }

            // Update invoice totals
            $totalAmount = $subtotal + $invoice->tax_amount + $invoice->shipping_cost - $invoice->discount_amount;
            $invoice->update([
                'subtotal' => $subtotal,
                'total_amount' => $totalAmount
            ]);

            DB::commit();

            return redirect()->route('admin.invoices.show', $invoice->id)
                           ->with('success', 'Invoice updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                           ->with('error', 'Error updating invoice: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Mark invoice as sent
     */
    public function markAsSent($id)
    {
        $invoice = Invoice::findOrFail($id);
        
        if ($invoice->status !== 'draft') {
            return redirect()->back()->with('error', 'Only draft invoices can be marked as sent.');
        }

        $invoice->markAsSent();

        return redirect()->back()->with('success', 'Invoice marked as sent.');
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid(Request $request, $id)
    {
        $invoice = Invoice::findOrFail($id);
        
        if ($invoice->status === 'paid') {
            return redirect()->back()->with('error', 'Invoice is already marked as paid.');
        }

        $paymentDetails = null;
        if ($request->filled('payment_method') || $request->filled('transaction_id')) {
            $paymentDetails = [
                'payment_method' => $request->payment_method,
                'transaction_id' => $request->transaction_id,
                'payment_date' => now()->toDateString(),
                'notes' => $request->payment_notes
            ];
        }

        $invoice->markAsPaid($paymentDetails);

        return redirect()->back()->with('success', 'Invoice marked as paid.');
    }

    /**
     * Generate PDF for invoice
     */
    public function generatePdf($id)
    {
        $invoice = Invoice::with(['order', 'items.product', 'items.color', 'createdBy'])
                         ->findOrFail($id);

        $pdf = \PDF::loadView('backend.invoices.pdf', compact('invoice'));
        
        return $pdf->download($invoice->invoice_number . '.pdf');
    }
}
