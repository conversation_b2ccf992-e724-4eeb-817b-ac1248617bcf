<?php

namespace App\Listeners;

use App\Events\OrderCompleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use App\Mail\MailQueue;
use App\Models\User;

class SendOrderCompletionNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderCompleted  $event
     * @return void
     */
    public function handle(OrderCompleted $event)
    {
        $order = $event->order;
        $completedBy = $event->completedBy;

        // Prepare order completion data
        $orderData = [
            'order' => $order,
            'completed_by' => $completedBy,
            'total_boxes' => $order->boxes()->count(),
            'total_packed_items' => $order->boxes()->withCount('items')->get()->sum('items_count'),
            'has_backorders' => $order->hasBackorders(),
            'shipping_cost' => $order->shipping_cost ?? 0,
            'box_summary' => $this->getBoxSummary($order),
            'packing_summary' => $this->getPackingSummary($order)
        ];

        // Send notification to office users (admin role)
        $officeUsers = User::where('role', 'admin')->where('status', 'active')->get();
        foreach ($officeUsers as $officeUser) {
            Mail::to($officeUser->email, $officeUser->first_name . ' ' . $officeUser->last_name)
                ->queue(new MailQueue(
                    'backend.emails.order_completion_office',
                    $orderData,
                    'Order Completed - Ready for Invoicing #' . $order->order_number
                ));
        }

        // Send notification to assigned salesman
        if ($order->salesman_id) {
            $salesman = $order->salesman;
            if ($salesman) {
                Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
                    ->queue(new MailQueue(
                        'backend.emails.order_completion_salesman',
                        $orderData,
                        'Order Completed - #' . $order->order_number
                    ));
            }
        }

        // Log the notification
        \Log::info('Order completion notifications sent', [
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'completed_by' => $completedBy->id,
            'office_users_notified' => $officeUsers->count(),
            'salesman_notified' => $order->salesman_id ? true : false
        ]);
    }

    /**
     * Get box summary for the order
     */
    private function getBoxSummary($order)
    {
        return $order->boxes()->with('items.product')->get()->map(function ($box) {
            return [
                'box_number' => $box->box_number,
                'weight' => $box->weight,
                'dimensions' => [
                    'length' => $box->length,
                    'width' => $box->width,
                    'height' => $box->height
                ],
                'item_count' => $box->items->count(),
                'total_quantity' => $box->items->sum('packed_quantity')
            ];
        });
    }

    /**
     * Get packing summary for the order
     */
    private function getPackingSummary($order)
    {
        $originalItems = $order->cart_info;
        $packedItems = $order->boxes()->with('items.product')->get()->flatMap->items;
        
        return [
            'original_item_count' => $originalItems->count(),
            'original_total_quantity' => $originalItems->sum('quantity'),
            'packed_item_count' => $packedItems->count(),
            'packed_total_quantity' => $packedItems->sum('packed_quantity'),
            'completion_percentage' => $originalItems->count() > 0 ? 
                round(($packedItems->sum('packed_quantity') / $originalItems->sum('quantity')) * 100, 2) : 0
        ];
    }
}
