@extends('backend.layouts.master')

@section('title', 'Order Summary - #' . $order->order_number)

@section('admin-content')
<div class="page-title-area">
    <div class="row align-items-center">
        <div class="col-sm-6">
            <div class="breadcrumbs-area clearfix">
                <h4 class="page-title pull-left">Order Summary</h4>
                <ul class="breadcrumbs pull-left">
                    <li><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li><a href="{{ route('orders.index') }}">Orders</a></li>
                    <li><span>Order Summary</span></li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6 clearfix">
            <div class="user-profile pull-right">
                <img class="avatar user-thumb" src="{{ asset('backend/assets/images/author/avatar.png') }}" alt="avatar">
                <h4 class="user-name dropdown-toggle" data-toggle="dropdown">{{ Auth::user()->first_name }} <i class="fa fa-angle-down"></i></h4>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#">Message</a>
                    <a class="dropdown-item" href="#">Settings</a>
                    <a class="dropdown-item" href="#">Log Out</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="main-content-inner">
    <div class="row">
        <!-- Order Information Card -->
        <div class="col-lg-12">
            <div class="card mt-5">
                <div class="card-header">
                    <h4 class="header-title d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-box"></i> Order #{{ $order->order_number }}
                            @if($order->quote_number)
                                <small class="text-muted">(Quote: {{ $order->quote_number }})</small>
                            @endif
                        </span>
                        <div class="btn-group">
                            <a href="{{ route('orders.show', $order->id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i> View Order
                            </a>
                            @if($order->invoice)
                                <a href="{{ route('admin.invoices.show', $order->invoice->id) }}" class="btn btn-success btn-sm">
                                    <i class="fas fa-file-invoice"></i> View Invoice
                                </a>
                            @else
                                <a href="{{ route('admin.invoices.create-from-order', $order->id) }}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-plus"></i> Create Invoice
                                </a>
                            @endif
                        </div>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Customer Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary"><i class="fas fa-user"></i> Customer Information</h5>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $order->first_name }} {{ $order->last_name }}</td>
                                </tr>
                                @if($order->company_name)
                                <tr>
                                    <td><strong>Company:</strong></td>
                                    <td>{{ $order->company_name }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $order->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ $order->phone }}</td>
                                </tr>
                                @if($order->salesman)
                                <tr>
                                    <td><strong>Salesman:</strong></td>
                                    <td>{{ $order->salesman->first_name }} {{ $order->salesman->last_name }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>

                        <!-- Order Details -->
                        <div class="col-md-6">
                            <h5 class="text-primary"><i class="fas fa-info-circle"></i> Order Details</h5>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $order->status == 'shipped' ? 'success' : 'warning' }}">
                                            {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $order->created_at->format('M d, Y g:i A') }}</td>
                                </tr>
                                @if($order->picker)
                                <tr>
                                    <td><strong>Picker:</strong></td>
                                    <td>{{ $order->picker->first_name }} {{ $order->picker->last_name }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td><strong>Delivery Method:</strong></td>
                                    <td>{{ ucfirst($order->delivery_method ?? 'Standard') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td><strong>${{ number_format($order->total_amount, 2) }}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Packing Statistics -->
        <div class="col-lg-12">
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="header-title"><i class="fas fa-chart-bar"></i> Packing Statistics</h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <div class="single-report mb-xs-30">
                                <div class="s-report-inner pr--20 pt--30 pb--20">
                                    <div class="icon"><i class="fas fa-boxes"></i></div>
                                    <div class="s-report-title d-flex justify-content-between">
                                        <h4 class="header-title mb-0">{{ $packingStats['total_boxes'] }}</h4>
                                    </div>
                                    <span>Total Boxes</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="single-report mb-xs-30">
                                <div class="s-report-inner pr--20 pt--30 pb--20">
                                    <div class="icon"><i class="fas fa-cube"></i></div>
                                    <div class="s-report-title d-flex justify-content-between">
                                        <h4 class="header-title mb-0">{{ $packingStats['packed_items'] }}</h4>
                                    </div>
                                    <span>Packed Items</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="single-report mb-xs-30">
                                <div class="s-report-inner pr--20 pt--30 pb--20">
                                    <div class="icon"><i class="fas fa-sort-numeric-up"></i></div>
                                    <div class="s-report-title d-flex justify-content-between">
                                        <h4 class="header-title mb-0">{{ $packingStats['packed_quantity'] }}</h4>
                                    </div>
                                    <span>Total Quantity</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="single-report mb-xs-30">
                                <div class="s-report-inner pr--20 pt--30 pb--20">
                                    <div class="icon"><i class="fas fa-percentage"></i></div>
                                    <div class="s-report-title d-flex justify-content-between">
                                        <h4 class="header-title mb-0">{{ $packingStats['completion_percentage'] }}%</h4>
                                    </div>
                                    <span>Completion</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="single-report mb-xs-30">
                                <div class="s-report-inner pr--20 pt--30 pb--20">
                                    <div class="icon"><i class="fas fa-shipping-fast"></i></div>
                                    <div class="s-report-title d-flex justify-content-between">
                                        <h4 class="header-title mb-0">${{ number_format($packingStats['shipping_cost'], 2) }}</h4>
                                    </div>
                                    <span>Shipping Cost</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="single-report mb-xs-30">
                                <div class="s-report-inner pr--20 pt--30 pb--20">
                                    <div class="icon"><i class="fas fa-{{ $packingStats['has_backorders'] ? 'exclamation-triangle' : 'check-circle' }}"></i></div>
                                    <div class="s-report-title d-flex justify-content-between">
                                        <h4 class="header-title mb-0">{{ $packingStats['has_backorders'] ? 'Yes' : 'No' }}</h4>
                                    </div>
                                    <span>Backorders</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Box Contents -->
        @if($boxSummary && count($boxSummary) > 0)
        <div class="col-lg-12">
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="header-title"><i class="fas fa-list-alt"></i> Box Contents Summary</h4>
                </div>
                <div class="card-body">
                    @foreach($boxSummary as $box)
                    <div class="box-summary mb-4 p-3 border rounded">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="text-primary">
                                    <i class="fas fa-box"></i> Box {{ $box['box_number'] }}
                                    <small class="text-muted">
                                        ({{ $box['item_count'] }} items, {{ $box['total_quantity'] }} total quantity)
                                    </small>
                                </h5>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="box-details">
                                    @if($box['weight'])
                                        <span class="badge badge-info">{{ $box['weight'] }} lbs</span>
                                    @endif
                                    @if($box['dimensions']['length'] && $box['dimensions']['width'] && $box['dimensions']['height'])
                                        <span class="badge badge-secondary">
                                            {{ $box['dimensions']['length'] }}" × {{ $box['dimensions']['width'] }}" × {{ $box['dimensions']['height'] }}"
                                        </span>
                                    @endif
                                    <span class="badge badge-success">${{ number_format($box['total_value'], 2) }}</span>
                                </div>
                            </div>
                        </div>
                        
                        @if(count($box['items']) > 0)
                        <div class="table-responsive mt-3">
                            <table class="table table-sm table-striped">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Item Number</th>
                                        <th>Product</th>
                                        <th>Color</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($box['items'] as $item)
                                    <tr>
                                        <td>{{ $item['item_number'] ?? 'N/A' }}</td>
                                        <td>{{ $item['product_name'] }}</td>
                                        <td>{{ $item['color_name'] ?? 'N/A' }}</td>
                                        <td>{{ $item['packed_quantity'] }}</td>
                                        <td>${{ number_format($item['unit_price'], 2) }}</td>
                                        <td>${{ number_format($item['line_total'], 2) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- Actions -->
        <div class="col-lg-12">
            <div class="card mt-4">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Orders
                        </a>
                        <a href="{{ route('orders.show', $order->id) }}" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Full Order
                        </a>
                        @if($order->status === 'shipped' && !$order->invoice)
                        <a href="{{ route('admin.invoices.create-from-order', $order->id) }}" class="btn btn-success">
                            <i class="fas fa-file-invoice"></i> Create Invoice
                        </a>
                        @endif
                        @if($order->invoice)
                        <a href="{{ route('admin.invoices.show', $order->invoice->id) }}" class="btn btn-info">
                            <i class="fas fa-file-invoice-dollar"></i> View Invoice
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.single-report {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.single-report:hover {
    transform: translateY(-2px);
}

.s-report-inner {
    text-align: center;
}

.s-report-inner .icon {
    font-size: 2rem;
    color: #4D734E;
    margin-bottom: 10px;
}

.box-summary {
    background: #f8f9fa;
    border-left: 4px solid #4D734E !important;
}

.box-details .badge {
    margin-left: 5px;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}
</style>
@endsection
