@extends('salesman.layouts.master')

@section('title','Backorders')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
           @include('backend.layouts.notification')
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4 px-3 pt-3">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Backorders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_pending'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Fulfilled Backorders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_fulfilled'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['total_value'], 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Overdue (30+ days)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['overdue_count'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">My Customer Backorders</h6>
        <div class="float-right">
            <a href="{{ route('salesman.orders.create') }}" class="btn btn-primary btn-sm" data-toggle="tooltip" data-placement="bottom" title="Create New Order">
                <i class="fas fa-plus"></i> New Order
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card-body border-bottom">
        <form method="GET" action="{{ route('salesman.backorders.index') }}" class="row">
            <div class="col-md-4">
                <label for="customer_id">Filter by Customer:</label>
                <select name="customer_id" id="customer_id" class="form-control">
                    <option value="">All Customers</option>
                    @foreach($customers as $customer)
                        <option value="{{ $customer->id }}" {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                            {{ $customer->first_name }} {{ $customer->last_name }} ({{ $customer->email }})
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label for="status">Filter by Status:</label>
                <select name="status" id="status" class="form-control">
                    <option value="">All Statuses</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="fulfilled" {{ request('status') == 'fulfilled' ? 'selected' : '' }}>Fulfilled</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary mr-2">Filter</button>
                <a href="{{ route('salesman.backorders.index') }}" class="btn btn-secondary">Clear</a>
            </div>
        </form>
    </div>

    <div class="card-body">
        <div class="table-responsive">
            @if(count($backorders) > 0)
            <table class="table table-bordered" id="backorder-dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Original Order</th>
                        <th>Customer</th>
                        <th>Product</th>
                        <th>Color</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Age</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($backorders as $backorder)
                        <tr class="{{ $backorder->created_at->diffInDays() > 30 ? 'table-warning' : '' }}">
                            <td>
                                <strong>{{ $backorder->originalOrder->order_number }}</strong>
                                @if($backorder->originalOrder->quote_number)
                                    <br><small class="text-muted">Quote: {{ $backorder->originalOrder->quote_number }}</small>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $backorder->originalOrder->first_name }} {{ $backorder->originalOrder->last_name }}</strong>
                                <br><small class="text-muted">{{ $backorder->originalOrder->email }}</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($backorder->product && $backorder->product->photo)
                                        <img src="{{ $backorder->product->photo }}" class="img-fluid zoom" style="max-width:50px" alt="{{ $backorder->product->title }}">
                                    @endif
                                    <div class="ml-2">
                                        <strong>{{ $backorder->product->title ?? 'Product Deleted' }}</strong>
                                        @if($backorder->product && $backorder->product->item_number)
                                            <br><small class="text-muted">Item #: {{ $backorder->product->item_number }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>{{ $backorder->color->name ?? 'N/A' }}</td>
                            <td><span class="badge badge-primary">{{ $backorder->quantity }}</span></td>
                            <td>${{ number_format($backorder->price, 2) }}</td>
                            <td><strong>${{ number_format($backorder->total_amount, 2) }}</strong></td>
                            <td>
                                @if($backorder->status == 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($backorder->status == 'fulfilled')
                                    <span class="badge badge-success">Fulfilled</span>
                                    @if($backorder->newOrder)
                                        <br><small class="text-muted">Order: {{ $backorder->newOrder->order_number }}</small>
                                    @endif
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-{{ $backorder->created_at->diffInDays() > 30 ? 'danger' : 'info' }}">
                                    {{ $backorder->created_at->diffForHumans() }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('salesman.backorders.customer', $backorder->originalOrder->user_id) }}" 
                                       class="btn btn-info btn-sm" data-toggle="tooltip" title="View Customer Backorders">
                                        <i class="fas fa-user"></i>
                                    </a>
                                    @if($backorder->status == 'pending')
                                        <button class="btn btn-success btn-sm create-order-btn" 
                                                data-customer-id="{{ $backorder->originalOrder->user_id }}"
                                                data-backorder-id="{{ $backorder->id }}"
                                                data-toggle="tooltip" title="Create Order with this Backorder">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <span style="float:right">{{ $backorders->links() }}</span>
            @else
                <h6 class="text-center">No backorders found!</h6>
            @endif
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Handle create order with backorder
    $('.create-order-btn').click(function() {
        const customerId = $(this).data('customer-id');
        const backorderId = $(this).data('backorder-id');
        
        Swal.fire({
            title: 'Create Order with Backorder',
            text: 'Do you want to create a new order and include this backorder item?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, create order'
        }).then((result) => {
            if (result.isConfirmed) {
                // Redirect to order creation with backorder pre-selected
                window.location.href = `{{ route('salesman.orders.create') }}?customer_id=${customerId}&backorder_id=${backorderId}`;
            }
        });
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
@endsection
