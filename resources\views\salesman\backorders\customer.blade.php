@extends('salesman.layouts.master')

@section('title','Customer Backorders - ' . $customer->first_name . ' ' . $customer->last_name)

@section('main-content')
<!-- Customer Info Card -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user"></i> {{ $customer->first_name }} {{ $customer->last_name }} - Backorders
                </h6>
                <small class="text-muted">{{ $customer->email }}</small>
            </div>
            <div class="col-auto">
                <div class="btn-group">
                    <a href="{{ route('salesman.backorders.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to All Backorders
                    </a>
                    <a href="{{ route('salesman.orders.create', ['customer_id' => $customer->id]) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Create New Order
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if($summary && $summary['total_backorders'] > 0)
    <div class="card-body">
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> Backorder Summary</h5>
            <div class="row">
                <div class="col-md-3">
                    <strong>Total Backorders:</strong> {{ $summary['total_backorders'] }}
                </div>
                <div class="col-md-3">
                    <strong>Total Quantity:</strong> {{ $summary['total_quantity'] }} items
                </div>
                <div class="col-md-3">
                    <strong>Total Value:</strong> ${{ number_format($summary['total_value'], 2) }}
                </div>
                <div class="col-md-3">
                    @if($summary['overdue_count'] > 0)
                        <strong class="text-danger">Overdue:</strong> {{ $summary['overdue_count'] }} items
                    @else
                        <strong class="text-success">No overdue items</strong>
                    @endif
                </div>
            </div>
            
            @if(count($backorders) > 0)
            <div class="mt-3">
                <button class="btn btn-success btn-sm" id="combine-all-btn">
                    <i class="fas fa-layer-group"></i> Combine All Backorders with New Order
                </button>
                <button class="btn btn-info btn-sm" id="select-combine-btn">
                    <i class="fas fa-check-square"></i> Select Items to Combine
                </button>
            </div>
            @endif
        </div>
    </div>
    @endif
</div>

<!-- Backorders Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Backorder Details</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($backorders) > 0)
            <table class="table table-bordered" id="backorder-dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all-backorders" class="form-check-input">
                        </th>
                        <th>Original Order</th>
                        <th>Product</th>
                        <th>Color</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Age</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($backorders as $backorder)
                        <tr class="{{ $backorder->created_at->diffInDays() > 30 ? 'table-warning' : '' }}">
                            <td>
                                @if($backorder->status == 'pending')
                                    <input type="checkbox" class="backorder-checkbox form-check-input" 
                                           value="{{ $backorder->id }}" 
                                           data-product-id="{{ $backorder->product_id }}"
                                           data-color-id="{{ $backorder->color_id }}"
                                           data-quantity="{{ $backorder->quantity }}"
                                           data-price="{{ $backorder->price }}">
                                @endif
                            </td>
                            <td>
                                <strong>{{ $backorder->originalOrder->order_number }}</strong>
                                @if($backorder->originalOrder->quote_number)
                                    <br><small class="text-muted">Quote: {{ $backorder->originalOrder->quote_number }}</small>
                                @endif
                                <br><small class="text-muted">{{ $backorder->originalOrder->created_at->format('M d, Y') }}</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($backorder->product && $backorder->product->photo)
                                        <img src="{{ $backorder->product->photo }}" class="img-fluid zoom" style="max-width:50px" alt="{{ $backorder->product->title }}">
                                    @endif
                                    <div class="ml-2">
                                        <strong>{{ $backorder->product->title ?? 'Product Deleted' }}</strong>
                                        @if($backorder->product && $backorder->product->item_number)
                                            <br><small class="text-muted">Item #: {{ $backorder->product->item_number }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>{{ $backorder->color->name ?? 'N/A' }}</td>
                            <td><span class="badge badge-primary">{{ $backorder->quantity }}</span></td>
                            <td>${{ number_format($backorder->price, 2) }}</td>
                            <td><strong>${{ number_format($backorder->total_amount, 2) }}</strong></td>
                            <td>
                                @if($backorder->status == 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($backorder->status == 'fulfilled')
                                    <span class="badge badge-success">Fulfilled</span>
                                    @if($backorder->newOrder)
                                        <br><small class="text-muted">Order: {{ $backorder->newOrder->order_number }}</small>
                                    @endif
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-{{ $backorder->created_at->diffInDays() > 30 ? 'danger' : 'info' }}">
                                    {{ $backorder->created_at->diffForHumans() }}
                                </span>
                            </td>
                            <td>
                                @if($backorder->status == 'pending')
                                    <button class="btn btn-success btn-sm create-single-order-btn" 
                                            data-backorder-id="{{ $backorder->id }}"
                                            data-toggle="tooltip" title="Create Order with this Item">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                @else
                                    @if($backorder->newOrder)
                                        <a href="{{ route('salesman.orders.show', $backorder->newOrder->id) }}" 
                                           class="btn btn-info btn-sm" data-toggle="tooltip" title="View Fulfilling Order">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    @endif
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <span style="float:right">{{ $backorders->links() }}</span>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>No Backorders Found</h5>
                    <p class="text-muted">This customer has no pending backorders.</p>
                    <a href="{{ route('salesman.orders.create', ['customer_id' => $customer->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Order
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Selected Items Summary (Hidden by default) -->
<div class="card shadow mb-4" id="selected-summary" style="display: none;">
    <div class="card-header py-3 bg-info text-white">
        <h6 class="m-0 font-weight-bold">Selected Backorders for New Order</h6>
    </div>
    <div class="card-body">
        <div id="selected-items-list"></div>
        <div class="mt-3">
            <button class="btn btn-success" id="create-combined-order-btn">
                <i class="fas fa-shopping-cart"></i> Create Order with Selected Items
            </button>
            <button class="btn btn-secondary" id="clear-selection-btn">
                <i class="fas fa-times"></i> Clear Selection
            </button>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let selectedBackorders = [];

    // Handle select all checkbox
    $('#select-all-backorders').change(function() {
        $('.backorder-checkbox').prop('checked', this.checked);
        updateSelectedSummary();
    });

    // Handle individual checkbox changes
    $('.backorder-checkbox').change(function() {
        updateSelectedSummary();
        
        // Update select all checkbox
        const totalCheckboxes = $('.backorder-checkbox').length;
        const checkedCheckboxes = $('.backorder-checkbox:checked').length;
        $('#select-all-backorders').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update selected items summary
    function updateSelectedSummary() {
        selectedBackorders = [];
        let totalQuantity = 0;
        let totalValue = 0;

        $('.backorder-checkbox:checked').each(function() {
            const backorderId = $(this).val();
            const quantity = parseInt($(this).data('quantity'));
            const price = parseFloat($(this).data('price'));
            
            selectedBackorders.push(backorderId);
            totalQuantity += quantity;
            totalValue += (quantity * price);
        });

        if (selectedBackorders.length > 0) {
            $('#selected-summary').show();
            $('#selected-items-list').html(`
                <div class="alert alert-info">
                    <strong>${selectedBackorders.length}</strong> items selected | 
                    <strong>${totalQuantity}</strong> total quantity | 
                    <strong>$${totalValue.toFixed(2)}</strong> total value
                </div>
            `);
        } else {
            $('#selected-summary').hide();
        }
    }

    // Combine all backorders
    $('#combine-all-btn').click(function() {
        $('.backorder-checkbox').prop('checked', true);
        updateSelectedSummary();
        
        Swal.fire({
            title: 'Combine All Backorders',
            text: 'Create a new order with all pending backorders for this customer?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, create order'
        }).then((result) => {
            if (result.isConfirmed) {
                createCombinedOrder();
            }
        });
    });

    // Show selection mode
    $('#select-combine-btn').click(function() {
        $('#selected-summary').show();
        $('html, body').animate({
            scrollTop: $('#selected-summary').offset().top
        }, 500);
    });

    // Create order with selected items
    $('#create-combined-order-btn').click(function() {
        if (selectedBackorders.length === 0) {
            Swal.fire('Error', 'Please select at least one backorder item.', 'error');
            return;
        }

        Swal.fire({
            title: 'Create Combined Order',
            text: `Create a new order with ${selectedBackorders.length} selected backorder items?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, create order'
        }).then((result) => {
            if (result.isConfirmed) {
                createCombinedOrder();
            }
        });
    });

    // Clear selection
    $('#clear-selection-btn').click(function() {
        $('.backorder-checkbox').prop('checked', false);
        $('#select-all-backorders').prop('checked', false);
        updateSelectedSummary();
    });

    // Create single order
    $('.create-single-order-btn').click(function() {
        const backorderId = $(this).data('backorder-id');
        
        Swal.fire({
            title: 'Create Order',
            text: 'Create a new order with this backorder item?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, create order'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = `{{ route('salesman.orders.create') }}?customer_id={{ $customer->id }}&backorder_ids[]=${backorderId}`;
            }
        });
    });

    // Create combined order function
    function createCombinedOrder() {
        const backorderIds = selectedBackorders.length > 0 ? selectedBackorders : 
                           $('.backorder-checkbox').map(function() { return this.value; }).get();
        
        if (backorderIds.length === 0) {
            Swal.fire('Error', 'No backorder items found to combine.', 'error');
            return;
        }

        const params = new URLSearchParams();
        params.append('customer_id', '{{ $customer->id }}');
        backorderIds.forEach(id => params.append('backorder_ids[]', id));
        
        window.location.href = `{{ route('salesman.orders.create') }}?${params.toString()}`;
    }

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
@endsection
