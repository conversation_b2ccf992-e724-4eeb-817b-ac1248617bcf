@extends('backend.layouts.master')
@section('title','Lamart || Picker Dashboard')
@section('main-content')
<div class="container-fluid">
    @include('backend.layouts.notification')
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
      <h1 class="h3 mb-0 text-gray-800">Picker Dashboard</h1>
      <a href="{{route('picker.orders')}}" class="btn btn-primary btn-sm">
        <i class="fas fa-list"></i> View All Orders
      </a>
    </div>

    <!-- Content Row -->
    <div class="row">

      <!-- Total Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$total_orders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pending Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Orders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$pending_orders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clock fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Processing Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Being Packed</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$processing_orders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-box fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Completed Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$completed_orders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Urgent Orders Section -->
    @if($urgent_orders->count() > 0)
    <div class="row">
      <div class="col-xl-12 col-lg-12">
        <div class="card shadow mb-4 border-left-danger">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-danger">
              <i class="fas fa-exclamation-triangle"></i> Orders Waiting for Packing
            </h6>
            <a href="{{route('picker.orders')}}" class="btn btn-danger btn-sm">View All</a>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Total</th>
                    <th>Delivery</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($urgent_orders as $order)
                  <tr>
                    <td><strong>{{$order->order_number}}</strong></td>
                    <td>{{$order->first_name.' '.$order->last_name}}</td>
                    <td><span class="badge badge-info">{{$order->cart_info->count()}} items</span></td>
                    <td>${{number_format($order->total_amount,2)}}</td>
                    <td><span class="badge badge-secondary">{{ucfirst($order->delivery_method ?? 'N/A')}}</span></td>
                    <td>
                      <a href="{{route('picker.orders.show', $order->id)}}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> View
                      </a>
                      <button class="btn btn-success btn-sm start-packing" data-id="{{$order->id}}">
                        <i class="fas fa-play"></i> Start
                      </button>
                    </td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    @endif

    <div class="row">
      <!-- Recent Orders -->
      <div class="col-xl-12 col-lg-12">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
            <a href="{{route('picker.orders')}}" class="btn btn-primary btn-sm">View All</a>
          </div>
          <div class="card-body">
            @if($latest_orders->count() > 0)
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Total</th>
                    <th>Delivery Method</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($latest_orders as $order)
                  <tr>
                    <td><strong>{{$order->order_number}}</strong></td>
                    <td>{{$order->first_name.' '.$order->last_name}}</td>
                    <td><span class="badge badge-info">{{$order->cart_info->count()}} items</span></td>
                    <td>${{number_format($order->total_amount,2)}}</td>
                    <td><span class="badge badge-secondary">{{ucfirst($order->delivery_method ?? 'N/A')}}</span></td>
                    <td>
                      @if($order->status=='sent_to_warehouse')
                        <span class="badge badge-warning">Sent to Warehouse</span>
                      @elseif($order->status=='processing')
                        <span class="badge badge-info">Being Packed</span>
                      @elseif($order->status=='shipped')
                        <span class="badge badge-success">Completed</span>
                      @else
                        <span class="badge badge-primary">{{ucfirst($order->status)}}</span>
                      @endif
                    </td>
                    <td>{{$order->created_at->format('M d, Y')}}</td>
                    <td>
                      <a href="{{route('picker.orders.show', $order->id)}}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i>
                      </a>
                    </td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            @else
            <div class="text-center py-4">
              <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
              <h5 class="text-gray-500">No orders assigned yet</h5>
              <p class="text-gray-400">Orders will appear here when they are assigned to you.</p>
            </div>
            @endif
          </div>
        </div>
      </div>
    </div>

  </div>
@endsection

@push('styles')
<style>
  .border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
  }
  .badge {
    font-size: 0.8em;
  }
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="https://js.pusher.com/7.2/pusher.min.js"></script>
<script>
  // Start packing functionality
  $('.start-packing').click(function(e) {
      e.preventDefault();
      var order_id = $(this).data('id');
      var token = $("meta[name='csrf-token']").attr("content");

      $.ajax({
          url: "{{route('picker.orders.start-packing')}}",
          type: 'POST',
          data: {
              "_token": token,
              "order_id": order_id
          },
          success: function(response) {
              if(response.status) {
                  swal({
                      title: "Success!",
                      text: response.message,
                      type: "success"
                  }, function() {
                      location.reload();
                  });
              } else {
                  swal("Error!", response.message, "error");
              }
          },
          error: function() {
              swal("Error!", "Something went wrong", "error");
          }
      });
  });

  // Real-time notifications setup
  @if(auth()->user()->role === 'picker')
  $(document).ready(function() {
      // Initialize Pusher
      var pusher = new Pusher('{{ env("PUSHER_APP_KEY") }}', {
          cluster: '{{ env("PUSHER_APP_CLUSTER") }}',
          encrypted: true,
          authEndpoint: '/broadcasting/auth',
          auth: {
              headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
              }
          }
      });

      // Subscribe to picker-specific channel
      var pickerChannel = pusher.subscribe('private-picker.{{ auth()->id() }}');

      // Subscribe to general warehouse notifications
      var warehouseChannel = pusher.subscribe('warehouse-notifications');

      // Handle order assignment notifications
      pickerChannel.bind('order.assigned.picker', function(data) {
          showOrderAssignmentNotification(data);
      });

      warehouseChannel.bind('order.assigned.picker', function(data) {
          // Show notification to all warehouse users
          if (data.picker_name !== '{{ auth()->user()->first_name }} {{ auth()->user()->last_name }}') {
              showGeneralNotification(data);
          }
      });

      function showOrderAssignmentNotification(data) {
          // Show browser notification if supported
          if ("Notification" in window && Notification.permission === "granted") {
              var notification = new Notification("New Order Assignment", {
                  body: data.message,
                  icon: '/backend/img/logo.png',
                  badge: '/backend/img/logo.png'
              });

              notification.onclick = function() {
                  window.focus();
                  window.location.href = '/picker/orders/' + data.order_id + '/pack';
              };
          }

          // Show SweetAlert popup
          swal({
              title: "New Order Assignment!",
              text: data.message + "\n\nCustomer: " + data.customer_name + "\nItems: " + data.item_count + "\nTotal: $" + data.total_amount,
              type: "info",
              showCancelButton: true,
              confirmButtonText: "Start Packing",
              cancelButtonText: "View Later",
              closeOnConfirm: false
          }, function(isConfirm) {
              if (isConfirm) {
                  window.location.href = '/picker/orders/' + data.order_id + '/pack';
              } else {
                  location.reload(); // Refresh to show new order in dashboard
              }
          });

          // Update dashboard counters
          updateDashboardCounters();
      }

      function showGeneralNotification(data) {
          // Show a subtle notification for other warehouse users
          var alertHtml = '<div class="alert alert-info alert-dismissible fade show" role="alert">' +
                         '<strong>Order Assigned:</strong> Order #' + data.order_number + ' assigned to ' + data.picker_name +
                         '<button type="button" class="close" data-dismiss="alert">' +
                         '<span>&times;</span></button></div>';

          $('.container-fluid').prepend(alertHtml);

          // Auto-dismiss after 5 seconds
          setTimeout(function() {
              $('.alert-info').fadeOut();
          }, 5000);
      }

      function updateDashboardCounters() {
          // Refresh dashboard data via AJAX
          $.get('/picker/dashboard/counters', function(data) {
              if (data.success) {
                  $('.text-primary').parent().find('.h5').text(data.total_orders);
                  $('.text-warning').parent().find('.h5').text(data.pending_orders);
                  $('.text-success').parent().find('.h5').text(data.completed_orders);
                  $('.text-danger').parent().find('.h5').text(data.returned_orders);
              }
          });
      }

      // Request notification permission on page load
      if ("Notification" in window && Notification.permission === "default") {
          Notification.requestPermission();
      }
  });
  @endif
</script>
@endpush
