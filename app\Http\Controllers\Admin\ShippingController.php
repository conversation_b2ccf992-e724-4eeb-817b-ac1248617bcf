<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ShipStationService;
use App\Models\Order;
use App\Models\OrderBox;

class ShippingController extends Controller
{
    protected $shipStationService;

    public function __construct(ShipStationService $shipStationService)
    {
        $this->shipStationService = $shipStationService;
    }

    /**
     * Get shipping rates for an order
     */
    public function getShippingRates(Request $request, $orderId)
    {
        try {
            $order = Order::with('boxes')->findOrFail($orderId);
            
            // Calculate total dimensions and weight from all boxes
            $totalWeight = $order->boxes->sum('weight') ?: 1; // Default to 1 lb if no weight
            $totalVolume = $order->boxes->sum(function($box) {
                return ($box->length ?: 12) * ($box->width ?: 12) * ($box->height ?: 6);
            });
            
            // Estimate dimensions if not provided
            $estimatedLength = $order->boxes->max('length') ?: 12;
            $estimatedWidth = $order->boxes->max('width') ?: 12;
            $estimatedHeight = $order->boxes->sum('height') ?: 6;

            $fromAddress = [
                'postal_code' => config('services.shipstation.from_address.postal_code')
            ];

            $toAddress = [
                'state' => $order->state ?? $order->shipping_state,
                'postal_code' => $order->post_code ?? $order->shipping_zip,
                'country' => $order->country ?? 'US'
            ];

            $packages = [
                'weight' => $totalWeight,
                'length' => $estimatedLength,
                'width' => $estimatedWidth,
                'height' => $estimatedHeight
            ];

            $rates = $this->shipStationService->getCarrierRates($fromAddress, $toAddress, $packages);

            return response()->json([
                'status' => true,
                'rates' => $rates,
                'package_info' => $packages,
                'order_info' => [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'total_boxes' => $order->boxes->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error getting shipping rates: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create a shipment for an order
     */
    public function createShipment(Request $request, $orderId)
    {
        try {
            $request->validate([
                'carrier_code' => 'required|string',
                'service_code' => 'required|string'
            ]);

            $order = Order::with('boxes', 'user')->findOrFail($orderId);
            
            // Calculate total dimensions and weight
            $totalWeight = $order->boxes->sum('weight') ?: 1;
            $estimatedLength = $order->boxes->max('length') ?: 12;
            $estimatedWidth = $order->boxes->max('width') ?: 12;
            $estimatedHeight = $order->boxes->sum('height') ?: 6;

            $orderData = [
                'customer_name' => $order->first_name . ' ' . $order->last_name,
                'company_name' => $order->company_name,
                'address1' => $order->address1 ?? $order->shipping_address,
                'address2' => $order->address2,
                'city' => $order->city ?? $order->shipping_city,
                'state' => $order->state ?? $order->shipping_state,
                'postal_code' => $order->post_code ?? $order->shipping_zip,
                'country' => $order->country ?? 'US',
                'phone' => $order->phone,
                'weight' => $totalWeight,
                'length' => $estimatedLength,
                'width' => $estimatedWidth,
                'height' => $estimatedHeight
            ];

            $shipment = $this->shipStationService->createShipment(
                $orderData,
                $request->carrier_code,
                $request->service_code
            );

            if (isset($shipment['error'])) {
                return response()->json([
                    'status' => false,
                    'message' => $shipment['error']
                ]);
            }

            // Update order with tracking information
            $order->update([
                'tracking_number' => $shipment['trackingNumber'] ?? null,
                'shipping_label_url' => $shipment['labelData'] ?? null,
                'carrier_code' => $request->carrier_code,
                'service_code' => $request->service_code,
                'shipping_cost' => $shipment['shipmentCost'] ?? 0,
                'status' => 'shipped'
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Shipment created successfully',
                'shipment' => $shipment,
                'tracking_number' => $shipment['trackingNumber'] ?? null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error creating shipment: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test ShipStation connection
     */
    public function testConnection()
    {
        try {
            $isConnected = $this->shipStationService->testConnection();
            
            return response()->json([
                'status' => $isConnected,
                'message' => $isConnected ? 'ShipStation connection successful' : 'ShipStation connection failed'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get available carriers and services
     */
    public function getCarriers()
    {
        $carriers = [
            'fedex' => [
                'name' => 'FedEx',
                'services' => [
                    'fedex_ground' => 'FedEx Ground',
                    'fedex_2_day' => 'FedEx 2Day',
                    'fedex_express_saver' => 'FedEx Express Saver',
                    'fedex_standard_overnight' => 'FedEx Standard Overnight',
                    'fedex_priority_overnight' => 'FedEx Priority Overnight'
                ]
            ],
            'ups' => [
                'name' => 'UPS',
                'services' => [
                    'ups_ground' => 'UPS Ground',
                    'ups_3_day_select' => 'UPS 3 Day Select',
                    'ups_2nd_day_air' => 'UPS 2nd Day Air',
                    'ups_next_day_air' => 'UPS Next Day Air',
                    'ups_next_day_air_saver' => 'UPS Next Day Air Saver'
                ]
            ],
            'stamps_com' => [
                'name' => 'USPS',
                'services' => [
                    'usps_first_class_mail' => 'USPS First-Class Mail',
                    'usps_priority_mail' => 'USPS Priority Mail',
                    'usps_priority_mail_express' => 'USPS Priority Mail Express',
                    'usps_ground_advantage' => 'USPS Ground Advantage',
                    'usps_media_mail' => 'USPS Media Mail'
                ]
            ]
        ];

        return response()->json([
            'status' => true,
            'carriers' => $carriers
        ]);
    }

    /**
     * Display shipping rates in the order packing interface
     */
    public function showRatesForOrder($orderId)
    {
        try {
            $order = Order::with('boxes')->findOrFail($orderId);
            
            return view('admin.shipping.rates', compact('order'));

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Order not found');
        }
    }

    /**
     * Get quick shipping estimate
     */
    public function getQuickEstimate(Request $request)
    {
        try {
            $request->validate([
                'weight' => 'required|numeric|min:0.1',
                'to_state' => 'required|string|max:2',
                'to_postal_code' => 'required|string|max:10'
            ]);

            $fromAddress = [
                'postal_code' => config('services.shipstation.from_address.postal_code')
            ];

            $toAddress = [
                'state' => $request->to_state,
                'postal_code' => $request->to_postal_code,
                'country' => 'US'
            ];

            $packages = [
                'weight' => $request->weight,
                'length' => $request->length ?? 12,
                'width' => $request->width ?? 12,
                'height' => $request->height ?? 6
            ];

            $rates = $this->shipStationService->getCarrierRates($fromAddress, $toAddress, $packages);

            return response()->json([
                'status' => true,
                'rates' => $rates
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error getting estimate: ' . $e->getMessage()
            ]);
        }
    }
}
