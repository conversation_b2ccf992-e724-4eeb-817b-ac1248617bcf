<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'order_id',
        'created_by',
        'status',
        'customer_name',
        'customer_email',
        'customer_phone',
        'company_name',
        'billing_address',
        'shipping_address',
        'subtotal',
        'tax_amount',
        'shipping_cost',
        'discount_amount',
        'total_amount',
        'invoice_date',
        'due_date',
        'sent_at',
        'paid_at',
        'notes',
        'terms',
        'payment_details'
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'sent_at' => 'datetime',
        'paid_at' => 'datetime',
        'payment_details' => 'array'
    ];

    /**
     * Relationships
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Generate unique invoice number
     */
    public static function generateInvoiceNumber()
    {
        $year = date('Y');
        $month = date('m');

        // Get the last invoice number for this month
        $lastInvoice = static::where('invoice_number', 'like', "INV-{$year}{$month}%")
                           ->orderBy('invoice_number', 'desc')
                           ->first();

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return "INV-{$year}{$month}{$newNumber}";
    }

    /**
     * Create invoice from completed order
     */
    public static function createFromOrder(Order $order, User $createdBy)
    {
        // Calculate totals from packed quantities
        $subtotal = 0;
        $packedItems = $order->boxes()->with('items.product', 'items.color_name')->get()->flatMap->items;

        $invoice = static::create([
            'invoice_number' => static::generateInvoiceNumber(),
            'order_id' => $order->id,
            'created_by' => $createdBy->id,
            'status' => 'draft',
            'customer_name' => $order->first_name . ' ' . $order->last_name,
            'customer_email' => $order->email,
            'customer_phone' => $order->phone,
            'company_name' => $order->company_name,
            'billing_address' => $order->getFullAddress(),
            'shipping_address' => $order->getFullShippingAddress(),
            'subtotal' => 0, // Will be calculated below
            'tax_amount' => 0,
            'shipping_cost' => $order->shipping_cost ?? 0,
            'discount_amount' => 0,
            'total_amount' => 0, // Will be calculated below
            'invoice_date' => now()->toDateString(),
            'due_date' => now()->addDays(30)->toDateString(),
            'terms' => 'Payment due within 30 days'
        ]);

        // Create invoice items from packed items
        foreach ($packedItems as $packedItem) {
            $originalCartItem = $order->cart_info->where('product_id', $packedItem->product_id)
                                                 ->where('color', $packedItem->color_id)
                                                 ->first();

            $lineTotal = $packedItem->packed_quantity * ($originalCartItem->price ?? 0);
            $subtotal += $lineTotal;

            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'product_id' => $packedItem->product_id,
                'color_id' => $packedItem->color_id,
                'item_number' => $packedItem->product->item_number ?? null,
                'product_name' => $packedItem->product->title ?? 'Unknown Product',
                'color_name' => $packedItem->color_name->name ?? null,
                'description' => $packedItem->product->summary ?? null,
                'original_quantity' => $originalCartItem->quantity ?? 0,
                'packed_quantity' => $packedItem->packed_quantity,
                'invoiced_quantity' => $packedItem->packed_quantity,
                'unit_price' => $originalCartItem->price ?? 0,
                'line_total' => $lineTotal,
                'cart_item_id' => $originalCartItem->id ?? null,
                'order_box_item_id' => $packedItem->id
            ]);
        }

        // Update invoice totals
        $totalAmount = $subtotal + $invoice->tax_amount + $invoice->shipping_cost - $invoice->discount_amount;
        $invoice->update([
            'subtotal' => $subtotal,
            'total_amount' => $totalAmount
        ]);

        return $invoice;
    }

    /**
     * Recalculate totals based on invoice items
     */
    public function recalculateTotals()
    {
        $subtotal = $this->items->sum('line_total');
        $totalAmount = $subtotal + $this->tax_amount + $this->shipping_cost - $this->discount_amount;

        $this->update([
            'subtotal' => $subtotal,
            'total_amount' => $totalAmount
        ]);
    }

    /**
     * Mark invoice as sent
     */
    public function markAsSent()
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now()
        ]);
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid($paymentDetails = null)
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'payment_details' => $paymentDetails
        ]);
    }

    /**
     * Check if invoice is overdue
     */
    public function isOverdue()
    {
        return $this->due_date && $this->due_date->isPast() && $this->status !== 'paid';
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'draft' => 'badge-secondary',
            'sent' => 'badge-primary',
            'paid' => 'badge-success',
            'cancelled' => 'badge-danger',
            default => 'badge-secondary'
        };
    }
}
