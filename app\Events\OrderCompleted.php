<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Order;
use App\Models\User;

class OrderCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    public $completedBy;

    /**
     * Create a new event instance.
     *
     * @param Order $order
     * @param User $completedBy
     * @return void
     */
    public function __construct(Order $order, User $completedBy)
    {
        $this->order = $order;
        $this->completedBy = $completedBy;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return [
            new Channel('office-notifications'),
            new PrivateChannel('admin-notifications')
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        // Calculate summary data
        $totalBoxes = $this->order->boxes()->count();
        $totalPackedItems = $this->order->boxes()->withCount('items')->get()->sum('items_count');
        $hasBackorders = $this->order->hasBackorders();
        $shippingCost = $this->order->shipping_cost ?? 0;

        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'quote_number' => $this->order->quote_number,
            'customer_name' => $this->order->first_name . ' ' . $this->order->last_name,
            'customer_company' => $this->order->company_name,
            'total_amount' => $this->order->total_amount,
            'total_boxes' => $totalBoxes,
            'total_packed_items' => $totalPackedItems,
            'has_backorders' => $hasBackorders,
            'shipping_cost' => $shippingCost,
            'completed_by' => $this->completedBy->first_name . ' ' . $this->completedBy->last_name,
            'completed_at' => now()->toISOString(),
            'salesman_name' => $this->order->salesman ? $this->order->salesman->first_name . ' ' . $this->order->salesman->last_name : null,
            'delivery_method' => $this->order->delivery_method,
            'message' => "Order #{$this->order->order_number} has been completed and is ready for invoicing"
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'order.completed';
    }
}
