<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ShippingAccess
{
    /**
     * Handle an incoming request for shipping functionality.
     * Allows admin, office, salesman, and picker roles.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $allowedRoles = ['admin', 'office', 'salesman', 'picker'];

        if(in_array($request->user()->role, $allowedRoles)){
            return $next($request);
        }
        else{
            request()->session()->flash('error','You do not have any permission to access this page');

            // Handle redirect based on user role
            $userRole = $request->user()->role;
            switch($userRole) {
                case 'admin':
                    return redirect()->route('admin');
                case 'salesman':
                    return redirect()->route('salesman');
                case 'picker':
                    return redirect()->route('picker');
                case 'user':
                case 'customer':
                    return redirect()->route('user');
                case 'office':
                    // Office users redirect to admin dashboard since no office route exists
                    return redirect()->route('admin');
                default:
                    return redirect()->route('login.form');
            }
        }
    }
}
