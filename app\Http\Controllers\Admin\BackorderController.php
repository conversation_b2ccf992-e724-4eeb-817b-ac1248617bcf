<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Backorder;
use App\Models\Order;
use App\Models\User;

class BackorderController extends Controller
{
    public function index()
    {
        $backorders = Backorder::with(['originalOrder', 'product', 'color', 'newOrder'])
                              ->latest()
                              ->paginate(20);

        return view('backend.backorders.index', compact('backorders'));
    }

    public function fulfill(Request $request, $id)
    {
        try {
            $request->validate([
                'fulfill_type' => 'required|in:new_order,mark_fulfilled',
                'notes' => 'nullable|string'
            ]);

            $backorder = Backorder::findOrFail($id);

            if ($backorder->status !== 'pending') {
                return response()->json([
                    'status' => false,
                    'message' => 'Backorder is not in pending status'
                ]);
            }

            if ($request->fulfill_type === 'new_order') {
                // Create new order for this backorder
                $newOrder = $this->createBackorderOrder($backorder);
                $backorder->fulfill($newOrder->id);

                $message = "Backorder fulfilled with new order #{$newOrder->order_number}";
            } else {
                // Just mark as fulfilled
                $backorder->fulfill();
                $message = "Backorder marked as fulfilled";
            }

            if ($request->notes) {
                $backorder->notes = $request->notes;
                $backorder->save();
            }

            return response()->json([
                'status' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to fulfill backorder: ' . $e->getMessage()
            ]);
        }
    }

    public function cancel(Request $request, $id)
    {
        try {
            $backorder = Backorder::findOrFail($id);

            if ($backorder->status !== 'pending') {
                return response()->json([
                    'status' => false,
                    'message' => 'Backorder is not in pending status'
                ]);
            }

            $reason = $request->input('reason', 'Cancelled by admin');
            $backorder->cancel($reason);

            return response()->json([
                'status' => true,
                'message' => 'Backorder cancelled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to cancel backorder: ' . $e->getMessage()
            ]);
        }
    }

    public function bulkFulfill(Request $request)
    {
        try {
            $request->validate([
                'backorder_ids' => 'required|array',
                'backorder_ids.*' => 'exists:backorders,id',
                'fulfill_type' => 'required|in:new_orders,mark_fulfilled'
            ]);

            $backorders = Backorder::whereIn('id', $request->backorder_ids)
                                  ->where('status', 'pending')
                                  ->get();

            if ($backorders->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => 'No pending backorders found'
                ]);
            }

            $fulfilledCount = 0;
            $newOrders = [];

            foreach ($backorders as $backorder) {
                if ($request->fulfill_type === 'new_orders') {
                    $newOrder = $this->createBackorderOrder($backorder);
                    $backorder->fulfill($newOrder->id);
                    $newOrders[] = $newOrder->order_number;
                } else {
                    $backorder->fulfill();
                }
                $fulfilledCount++;
            }

            $message = "Successfully fulfilled {$fulfilledCount} backorders";
            if (!empty($newOrders)) {
                $message .= ". New orders created: " . implode(', ', $newOrders);
            }

            return response()->json([
                'status' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to fulfill backorders: ' . $e->getMessage()
            ]);
        }
    }

    private function createBackorderOrder($backorder)
    {
        $originalOrder = $backorder->originalOrder;
        $customer = User::find($originalOrder->user_id);

        // Create new order for the backorder
        $newOrder = Order::create([
            'order_number' => 'BO-' . time() . '-' . rand(1000, 9999),
            'user_id' => $originalOrder->user_id,
            'salesman_id' => $originalOrder->salesman_id,
            'sub_total' => $backorder->total_amount,
            'total_amount' => $backorder->total_amount,
            'quantity' => $backorder->quantity,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->contact_phone ?? $customer->account_phone,
            'country' => $originalOrder->country,
            'post_code' => $originalOrder->post_code,
            'address1' => $originalOrder->address1,
            'address2' => $originalOrder->address2,
            'delivery_method' => $originalOrder->delivery_method,
            'delivery_instructions' => $originalOrder->delivery_instructions,
            'status' => 'pending'
        ]);

        // Create cart item for the new order
        \App\Models\Cart::create([
            'user_id' => $originalOrder->user_id,
            'product_id' => $backorder->product_id,
            'order_id' => $newOrder->id,
            'quantity' => $backorder->quantity,
            'price' => $backorder->price,
            'amount' => $backorder->total_amount,
            'color' => $backorder->color_id,
            'status' => 'new'
        ]);

        return $newOrder;
    }

    public function getCustomerOrders($customerId)
    {
        $orders = Order::where('user_id', $customerId)
                      ->whereIn('status', ['pending', 'processing'])
                      ->select('id', 'order_number', 'total_amount', 'status')
                      ->get();

        return response()->json($orders);
    }

    public function delete($id)
    {
        try {
            $backorder = Backorder::findOrFail($id);

            if ($backorder->status !== 'pending') {
                return response()->json([
                    'status' => false,
                    'message' => 'Only pending backorders can be deleted'
                ]);
            }

            $backorder->delete();

            return response()->json([
                'status' => true,
                'message' => 'Backorder deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error deleting backorder: ' . $e->getMessage()
            ]);
        }
    }

    public function bulkDelete(Request $request)
    {
        try {
            $request->validate([
                'backorder_ids' => 'required|array',
                'backorder_ids.*' => 'exists:backorders,id'
            ]);

            $deletedCount = Backorder::whereIn('id', $request->backorder_ids)
                                   ->where('status', 'pending')
                                   ->delete();

            return response()->json([
                'status' => true,
                'message' => "Successfully deleted {$deletedCount} backorder(s)"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error deleting backorders: ' . $e->getMessage()
            ]);
        }
    }
}
