<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained();
            $table->foreignId('color_id')->nullable()->constrained('colors');

            // Item details (copied for historical accuracy)
            $table->string('item_number')->nullable();
            $table->string('product_name');
            $table->string('color_name')->nullable();
            $table->text('description')->nullable();

            // Quantities and pricing
            $table->integer('original_quantity'); // Original order quantity
            $table->integer('packed_quantity'); // Actually packed quantity (editable)
            $table->integer('invoiced_quantity'); // Final invoiced quantity (may differ from packed)
            $table->decimal('unit_price', 10, 2);
            $table->decimal('line_total', 10, 2);

            // References
            $table->foreignId('cart_item_id')->nullable()->constrained('carts');
            $table->foreignId('order_box_id')->nullable()->constrained('order_boxes');

            $table->timestamps();

            $table->index(['invoice_id', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
