<?php

namespace App\Http\Controllers\Salesman;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Backorder;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class BackorderController extends Controller
{
    /**
     * Display backorders for salesman's customers
     */
    public function index(Request $request)
    {
        $query = Backorder::with(['originalOrder', 'product', 'color', 'newOrder'])
                          ->whereHas('originalOrder', function($q) {
                              $q->where('salesman_id', Auth::user()->id);
                          });

        // Filter by customer if specified
        if ($request->filled('customer_id')) {
            $query->whereHas('originalOrder', function($q) use ($request) {
                $q->where('user_id', $request->customer_id);
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $backorders = $query->latest()->paginate(20);

        // Get salesman's customers for filter dropdown
        $customers = User::whereIn('role', ['user', 'customer'])
                        ->where('salesman_id', Auth::user()->id)
                        ->where('status', 'active')
                        ->select('id', 'first_name', 'last_name', 'email')
                        ->get();

        // Get statistics
        $stats = $this->getBackorderStats();

        return view('salesman.backorders.index', compact('backorders', 'customers', 'stats'));
    }

    /**
     * Show backorders for a specific customer
     */
    public function customerBackorders($customerId)
    {
        // Verify customer belongs to this salesman
        $customer = User::where('id', $customerId)
                       ->where('salesman_id', Auth::user()->id)
                       ->firstOrFail();

        $backorders = Backorder::with(['originalOrder', 'product', 'color', 'newOrder'])
                              ->whereHas('originalOrder', function($q) use ($customerId) {
                                  $q->where('user_id', $customerId);
                              })
                              ->latest()
                              ->paginate(20);

        $summary = Backorder::getBackorderSummary($customerId);

        return view('salesman.backorders.customer', compact('backorders', 'customer', 'summary'));
    }

    /**
     * Get backorder statistics for the salesman
     */
    private function getBackorderStats()
    {
        $salesmanId = Auth::user()->id;

        $totalBackorders = Backorder::whereHas('originalOrder', function($q) use ($salesmanId) {
                                   $q->where('salesman_id', $salesmanId);
                               })
                               ->where('status', 'pending')
                               ->count();

        $fulfilledBackorders = Backorder::whereHas('originalOrder', function($q) use ($salesmanId) {
                                       $q->where('salesman_id', $salesmanId);
                                   })
                                   ->where('status', 'fulfilled')
                                   ->count();

        $totalValue = Backorder::whereHas('originalOrder', function($q) use ($salesmanId) {
                              $q->where('salesman_id', $salesmanId);
                          })
                          ->where('status', 'pending')
                          ->sum('total_amount');

        $overdueBackorders = Backorder::whereHas('originalOrder', function($q) use ($salesmanId) {
                                     $q->where('salesman_id', $salesmanId);
                                 })
                                 ->where('status', 'pending')
                                 ->where('created_at', '<', now()->subDays(30))
                                 ->count();

        return [
            'total_pending' => $totalBackorders,
            'total_fulfilled' => $fulfilledBackorders,
            'total_value' => $totalValue,
            'overdue_count' => $overdueBackorders
        ];
    }

    /**
     * Check for backorders when creating new order (AJAX)
     */
    public function checkCustomerBackorders(Request $request)
    {
        $customerId = $request->customer_id;
        
        // Verify customer belongs to this salesman
        $customer = User::where('id', $customerId)
                       ->where('salesman_id', Auth::user()->id)
                       ->first();

        if (!$customer) {
            return response()->json(['has_backorders' => false]);
        }

        $backorders = Backorder::getCustomerBackorders($customerId);
        $summary = Backorder::getBackorderSummary($customerId);

        return response()->json([
            'has_backorders' => count($backorders) > 0,
            'backorders' => $backorders,
            'summary' => $summary,
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->first_name . ' ' . $customer->last_name,
                'email' => $customer->email
            ]
        ]);
    }

    /**
     * Combine selected backorders with new order
     */
    public function combineWithNewOrder(Request $request)
    {
        $request->validate([
            'backorder_ids' => 'required|array',
            'backorder_ids.*' => 'exists:backorders,id',
            'customer_id' => 'required|exists:users,id'
        ]);

        try {
            // Verify customer belongs to this salesman
            $customer = User::where('id', $request->customer_id)
                           ->where('salesman_id', Auth::user()->id)
                           ->firstOrFail();

            $backorders = Backorder::whereIn('id', $request->backorder_ids)
                                  ->where('status', 'pending')
                                  ->whereHas('originalOrder', function($q) {
                                      $q->where('salesman_id', Auth::user()->id);
                                  })
                                  ->get();

            if ($backorders->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => 'No valid backorders found to combine.'
                ]);
            }

            // Prepare backorder items for order creation
            $backorderItems = $backorders->map(function($backorder) {
                return [
                    'backorder_id' => $backorder->id,
                    'product_id' => $backorder->product_id,
                    'color_id' => $backorder->color_id,
                    'quantity' => $backorder->quantity,
                    'price' => $backorder->price,
                    'product_name' => $backorder->product->title ?? 'Unknown Product',
                    'color_name' => $backorder->color->name ?? 'N/A',
                    'item_number' => $backorder->product->item_number ?? 'N/A'
                ];
            });

            return response()->json([
                'status' => true,
                'message' => 'Backorders ready to combine',
                'backorder_items' => $backorderItems,
                'total_items' => $backorders->count(),
                'total_quantity' => $backorders->sum('quantity'),
                'total_value' => $backorders->sum('total_amount')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error combining backorders: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Mark backorders as fulfilled when combined with new order
     */
    public function markAsFulfilled(Request $request)
    {
        $request->validate([
            'backorder_ids' => 'required|array',
            'backorder_ids.*' => 'exists:backorders,id',
            'new_order_id' => 'required|exists:orders,id'
        ]);

        try {
            $backorders = Backorder::whereIn('id', $request->backorder_ids)
                                  ->where('status', 'pending')
                                  ->whereHas('originalOrder', function($q) {
                                      $q->where('salesman_id', Auth::user()->id);
                                  })
                                  ->get();

            foreach ($backorders as $backorder) {
                $backorder->update([
                    'status' => 'fulfilled',
                    'new_order_id' => $request->new_order_id,
                    'fulfilled_at' => now()
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => 'Backorders marked as fulfilled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error marking backorders as fulfilled: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get customer list with backorder counts for dashboard
     */
    public function getCustomersWithBackorders()
    {
        $customers = User::whereIn('role', ['user', 'customer'])
                        ->where('salesman_id', Auth::user()->id)
                        ->where('status', 'active')
                        ->withCount(['orders as pending_backorders_count' => function($query) {
                            $query->whereHas('backorders', function($q) {
                                $q->where('status', 'pending');
                            });
                        }])
                        ->having('pending_backorders_count', '>', 0)
                        ->get();

        return response()->json($customers);
    }
}
