<?php

namespace App\Http\Controllers;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PickerController extends Controller
{
    public function index()
    {
        $picker_id = Auth::user()->id;

        // Order statistics
        $total_orders = Order::where('picker_id', $picker_id)->count();
        $pending_orders = Order::where('picker_id', $picker_id)
                              ->where('status', 'sent_to_warehouse')
                              ->count();
        $processing_orders = Order::where('picker_id', $picker_id)
                                 ->where('status', 'processing')
                                 ->count();
        $completed_orders = Order::where('picker_id', $picker_id)
                                ->where('status', 'shipped')
                                ->count();

        // Latest orders with relationships
        $latest_orders = Order::where('picker_id', $picker_id)
                             ->with(['cart_info', 'user'])
                             ->latest()
                             ->limit(5)
                             ->get();

        // Orders that need immediate attention
        $urgent_orders = Order::where('picker_id', $picker_id)
                             ->where('status', 'sent_to_warehouse')
                             ->with(['cart_info', 'user'])
                             ->latest()
                             ->limit(3)
                             ->get();

        return view('picker.dashboard', compact(
            'total_orders',
            'pending_orders',
            'processing_orders',
            'completed_orders',
            'latest_orders',
            'urgent_orders'
        ));
    }

    public function getDashboardCounters()
    {
        $picker_id = Auth::user()->id;

        $data = [
            'success' => true,
            'total_orders' => Order::where('picker_id', $picker_id)->count(),
            'pending_orders' => Order::where('picker_id', $picker_id)
                                    ->where('status', 'sent_to_warehouse')
                                    ->count(),
            'completed_orders' => Order::where('picker_id', $picker_id)
                                      ->where('status', 'shipped')
                                      ->count(),
            'returned_orders' => Order::where('picker_id', $picker_id)
                                     ->where('return_status', '!=', 'none')
                                     ->count()
        ];

        return response()->json($data);
    }
}
