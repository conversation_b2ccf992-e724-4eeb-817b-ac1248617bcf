<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill of Lading - Order {{$order->order_number}}</title>
    <style>
        /* 4x6 inch label styling */
        @page {
            size: 4in 6in;
            margin: 0.1in;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 8px;
            width: 3.8in;
            height: 5.8in;
            background: white;
            color: black;
        }

        .label-container {
            width: 100%;
            height: 100%;
            border: 3px solid #000;
            padding: 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 8px;
            margin-bottom: 8px;
            background: #4D734E;
            color: white;
            margin: -8px -8px 8px -8px;
            padding: 8px;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .document-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .order-info {
            font-size: 9px;
        }

        .shipping-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 8px;
        }

        .address-block {
            border: 1px solid #000;
            padding: 6px;
            background: #f8f9fa;
        }

        .address-block h4 {
            margin: 0 0 4px 0;
            font-size: 10px;
            font-weight: bold;
            color: #4D734E;
            border-bottom: 1px solid #4D734E;
            padding-bottom: 2px;
        }

        .address {
            line-height: 1.2;
            font-size: 9px;
        }

        .shipment-details {
            border: 2px solid #000;
            padding: 6px;
            margin-bottom: 8px;
            background: #fff3cd;
        }

        .shipment-details h4 {
            margin: 0 0 6px 0;
            font-size: 11px;
            font-weight: bold;
            color: #000;
            text-align: center;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            font-size: 9px;
        }

        .boxes-section {
            flex: 1;
            border: 1px solid #000;
            padding: 6px;
            margin-bottom: 8px;
        }

        .boxes-section h4 {
            margin: 0 0 6px 0;
            font-size: 10px;
            font-weight: bold;
            color: #4D734E;
        }

        .box-list {
            font-size: 8px;
            line-height: 1.3;
        }

        .box-item {
            margin-bottom: 3px;
            padding: 3px;
            border: 1px solid #ddd;
            background: #f8f9fa;
        }

        .box-number {
            font-weight: bold;
            color: #4D734E;
        }

        .signatures-section {
            border: 2px solid #000;
            padding: 6px;
            margin-bottom: 6px;
        }

        .signatures-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 8px;
        }

        .signature-block {
            border: 1px solid #000;
            padding: 4px;
            height: 40px;
        }

        .signature-label {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            height: 20px;
            margin-top: 8px;
        }

        .footer {
            border-top: 2px solid #000;
            padding-top: 4px;
            text-align: center;
            font-size: 7px;
            background: #e9ecef;
            margin: 0 -8px -8px -8px;
            padding: 4px 8px 8px 8px;
        }

        .barcode-section {
            text-align: center;
            margin: 4px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            letter-spacing: 2px;
            border: 1px solid #000;
            padding: 4px;
            background: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        .urgent {
            background: #dc3545;
            color: white;
            padding: 4px;
            font-size: 10px;
            text-align: center;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .delivery-method {
            background: #17a2b8;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="label-container">
        <div class="header">
            <div class="company-name">LAMART</div>
            <div class="document-title">BILL OF LADING</div>
            <div class="order-info">
                Order #{{$order->order_number}} | Quote #{{$order->quote_number ?? 'N/A'}}
            </div>
        </div>

        @if($order->delivery_method == 'express' || $order->delivery_method == 'urgent')
        <div class="urgent">
            ⚡ {{strtoupper($order->delivery_method)}} DELIVERY REQUIRED
        </div>
        @endif

        <div class="shipping-section">
            <div class="address-block">
                <h4>📤 SHIP FROM:</h4>
                <div class="address">
                    <strong>LAMART WAREHOUSE</strong><br>
                    [Your Warehouse Address]<br>
                    [City, State ZIP]<br>
                    📞 [Phone Number]
                </div>
            </div>
            <div class="address-block">
                <h4>📦 SHIP TO:</h4>
                <div class="address">
                    <strong>{{$order->first_name}} {{$order->last_name}}</strong><br>
                    @if($order->company_name)<strong>{{$order->company_name}}</strong><br>@endif
                    {{$order->address1}}<br>
                    @if($order->address2){{$order->address2}}<br>@endif
                    {{$order->country}}, {{$order->post_code}}<br>
                    📞 {{$order->phone}}
                </div>
            </div>
        </div>

        <div class="shipment-details">
            <h4>📋 SHIPMENT DETAILS</h4>
            <div class="details-grid">
                <div><strong>Total Boxes:</strong></div>
                <div>{{$order->boxes->count()}} boxes</div>
                <div><strong>Total Weight:</strong></div>
                <div>{{$order->boxes->sum('weight') ?: 'TBD'}} lbs</div>
                <div><strong>Delivery Method:</strong></div>
                <div><span class="delivery-method">{{strtoupper($order->delivery_method ?? 'STANDARD')}}</span></div>
                <div><strong>Ship Date:</strong></div>
                <div>{{now()->format('m/d/Y')}}</div>
            </div>
        </div>

        <div class="boxes-section">
            <h4>📦 PACKAGE MANIFEST:</h4>
            <div class="box-list">
                @foreach($order->boxes as $box)
                <div class="box-item">
                    <span class="box-number">{{$box->box_label}}</span>
                    - {{$box->items->sum('packed_quantity')}} items
                    @if($box->weight)
                        - {{$box->weight}} lbs
                    @endif
                    @if($box->length && $box->width && $box->height)
                        <br><small>{{$box->length}}" × {{$box->width}}" × {{$box->height}}"</small>
                    @endif
                </div>
                @endforeach

                @if($order->boxes->count() == 0)
                <div style="text-align: center; color: #666; font-style: italic;">
                    No boxes created for this order
                </div>
                @endif
            </div>
        </div>

        <div class="signatures-section">
            <div class="signatures-grid">
                <div class="signature-block">
                    <div class="signature-label">CARRIER SIGNATURE:</div>
                    <div class="signature-line"></div>
                    <div style="font-size: 7px; margin-top: 2px;">Date: ___________</div>
                </div>
                <div class="signature-block">
                    <div class="signature-label">RECEIVER SIGNATURE:</div>
                    <div class="signature-line"></div>
                    <div style="font-size: 7px; margin-top: 2px;">Date: ___________</div>
                </div>
            </div>
        </div>

        <div class="barcode-section">
            BOL-{{$order->order_number}}-{{now()->format('Ymd')}}
        </div>

        <div class="footer">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                <div>
                    <strong>Prepared:</strong> {{now()->format('m/d/Y H:i')}}<br>
                    <strong>By:</strong> {{Auth::user()->first_name}} {{Auth::user()->last_name}}
                </div>
                <div>
                    <strong>Special Instructions:</strong><br>
                    @if($order->delivery_method == 'pickup')
                        CUSTOMER PICKUP
                    @elseif($order->delivery_method == 'truck')
                        TRUCK DELIVERY
                    @else
                        CARRIER SHIPMENT
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="no-print" style="position: fixed; bottom: 10px; right: 10px;">
        <button onclick="window.print()" style="padding: 8px 16px; background: #4D734E; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 5px;">
            🖨️ Print BOL
        </button>
        <button onclick="window.close()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            ✖️ Close
        </button>
    </div>
</body>
</html>
