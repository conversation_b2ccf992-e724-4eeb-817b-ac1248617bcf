@extends('backend.emails.layout')

@section('content')
<div class="email-header">
    <h2 style="color: #4D734E; margin-bottom: 20px;">
        <i class="fas fa-check-circle"></i> Order Completed - Ready for Invoicing
    </h2>
</div>

<div class="order-info">
    <div class="info-grid">
        <div class="info-item">
            <strong>Order Number:</strong>
            <span>{{ $order->order_number }}</span>
        </div>
        <div class="info-item">
            <strong>Quote Number:</strong>
            <span>{{ $order->quote_number }}</span>
        </div>
        <div class="info-item">
            <strong>Customer:</strong>
            <span>{{ $order->first_name }} {{ $order->last_name }}</span>
        </div>
        @if($order->company_name)
        <div class="info-item">
            <strong>Company:</strong>
            <span>{{ $order->company_name }}</span>
        </div>
        @endif
        <div class="info-item">
            <strong>Completed By:</strong>
            <span>{{ $completed_by->first_name }} {{ $completed_by->last_name }}</span>
        </div>
        <div class="info-item">
            <strong>Completed At:</strong>
            <span>{{ now()->format('M d, Y g:i A') }}</span>
        </div>
    </div>
</div>

<div class="packing-summary">
    <h3 style="color: #4D734E; border-bottom: 2px solid #4D734E; padding-bottom: 10px;">
        📦 Packing Summary
    </h3>
    
    <div class="summary-stats">
        <div class="stat-item">
            <div class="stat-number">{{ $total_boxes }}</div>
            <div class="stat-label">Total Boxes</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ $total_packed_items }}</div>
            <div class="stat-label">Packed Items</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">${{ number_format($order->total_amount, 2) }}</div>
            <div class="stat-label">Order Value</div>
        </div>
        @if($shipping_cost > 0)
        <div class="stat-item">
            <div class="stat-number">${{ number_format($shipping_cost, 2) }}</div>
            <div class="stat-label">Shipping Cost</div>
        </div>
        @endif
    </div>
</div>

@if($box_summary && count($box_summary) > 0)
<div class="box-details">
    <h3 style="color: #4D734E; border-bottom: 2px solid #4D734E; padding-bottom: 10px;">
        📋 Box Details
    </h3>
    
    <table class="details-table">
        <thead>
            <tr>
                <th>Box</th>
                <th>Items</th>
                <th>Quantity</th>
                <th>Weight</th>
                <th>Dimensions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($box_summary as $box)
            <tr>
                <td><strong>Box {{ $box['box_number'] }}</strong></td>
                <td>{{ $box['item_count'] }}</td>
                <td>{{ $box['total_quantity'] }}</td>
                <td>{{ $box['weight'] ? $box['weight'] . ' lbs' : 'TBD' }}</td>
                <td>
                    @if($box['dimensions']['length'] && $box['dimensions']['width'] && $box['dimensions']['height'])
                        {{ $box['dimensions']['length'] }}" × {{ $box['dimensions']['width'] }}" × {{ $box['dimensions']['height'] }}"
                    @else
                        Not specified
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
@endif

@if($has_backorders)
<div class="alert alert-warning">
    <h4><i class="fas fa-exclamation-triangle"></i> Backorders Created</h4>
    <p>Some items were not available and have been moved to backorders. Please review the backorder list for this customer.</p>
</div>
@endif

<div class="next-steps">
    <h3 style="color: #4D734E; border-bottom: 2px solid #4D734E; padding-bottom: 10px;">
        📝 Next Steps
    </h3>
    <ul>
        <li>Review packed quantities and create invoice</li>
        <li>Verify shipping costs and delivery method</li>
        @if($has_backorders)
        <li>Process any backorders for this customer</li>
        @endif
        <li>Coordinate delivery or shipping</li>
    </ul>
</div>

<div class="action-buttons">
    <a href="{{ url('/admin/orders/' . $order->id) }}" class="btn btn-primary">
        View Order Details
    </a>
    <a href="{{ url('/admin/orders/' . $order->id . '/invoice') }}" class="btn btn-success">
        Create Invoice
    </a>
</div>

<style>
.summary-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 5px;
    min-width: 120px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #4D734E;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.details-table th,
.details-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.details-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.next-steps ul {
    padding-left: 20px;
}

.next-steps li {
    margin: 8px 0;
}

.action-buttons {
    margin: 30px 0;
    text-align: center;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 0 10px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #4D734E;
    color: white;
}
</style>
@endsection
