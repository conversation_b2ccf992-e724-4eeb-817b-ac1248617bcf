@extends('layouts.app')

@section('title', 'Mobile Packing - Order #' . $order->order_number)

@section('content')
<div class="mobile-pack-container">
    <!-- Header -->
    <div class="mobile-header">
        <div class="header-top">
            <button class="btn-back" onclick="window.history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="order-info">
                <div class="order-number">{{$order->order_number}}</div>
                <div class="quote-number">{{$order->quote_number}}</div>
            </div>
            <button class="btn-menu" onclick="toggleMenu()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
            <div class="progress-text" id="progress-text">0 / {{$order->cart_info->count()}} items</div>
        </div>
    </div>

    <!-- Quick Actions Menu -->
    <div class="quick-menu" id="quick-menu">
        <button class="menu-item" onclick="openBarcodeScanner()">
            <i class="fas fa-barcode"></i>
            <span>Scan</span>
        </button>
        <button class="menu-item" onclick="printPackingList4x6()">
            <i class="fas fa-print"></i>
            <span>Print</span>
        </button>
        <button class="menu-item" onclick="completeOrder()">
            <i class="fas fa-check"></i>
            <span>Complete</span>
        </button>
    </div>

    <!-- Customer Info (Collapsible) -->
    <div class="customer-section">
        <div class="section-header" onclick="toggleSection('customer')">
            <i class="fas fa-user"></i>
            <span>{{$order->first_name}} {{$order->last_name}}</span>
            <i class="fas fa-chevron-down toggle-icon"></i>
        </div>
        <div class="section-content" id="customer-content">
            <div class="customer-details">
                <div><strong>Email:</strong> {{$order->email}}</div>
                <div><strong>Phone:</strong> {{$order->phone}}</div>
                <div><strong>Address:</strong> {{$order->address1}}</div>
                @if($order->address2)<div>{{$order->address2}}</div>@endif
                <div>{{$order->country}}, {{$order->post_code}}</div>
                @if($order->delivery_instructions)
                <div class="delivery-instructions">
                    <strong>Instructions:</strong> {{$order->delivery_instructions}}
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Items List -->
    <div class="items-container">
        @foreach($order->cart_info as $index => $item)
        <div class="mobile-item-card" data-item-id="{{$item->id}}" data-packed="{{$item->is_packed ? 'true' : 'false'}}">
            <div class="item-header">
                <div class="item-number">{{$item->product->item_number ?? 'N/A'}}</div>
                <div class="item-status">
                    @if($item->is_packed)
                        <i class="fas fa-check-circle text-success"></i>
                    @elseif($item->is_returned)
                        <i class="fas fa-times-circle text-danger"></i>
                    @else
                        <i class="far fa-circle text-muted"></i>
                    @endif
                </div>
            </div>

            <div class="item-body">
                <div class="item-title">{{$item->product->title}}</div>
                @if($item->color_name)
                <div class="item-color">Color: {{$item->color_name->name}}</div>
                @endif
                <div class="item-quantity">Qty: {{$item->quantity}}</div>
                @if($item->box_id)
                <div class="item-box">Box: {{$item->box->box_number ?? 'N/A'}}</div>
                @endif
            </div>

            <div class="item-actions">
                @if(!$item->is_packed && !$item->is_returned)
                <button class="btn-pack" onclick="packItem({{$item->id}})">
                    <i class="fas fa-box"></i>
                    Pack
                </button>
                <button class="btn-return" onclick="returnItem({{$item->id}})">
                    <i class="fas fa-undo"></i>
                    Return
                </button>
                @elseif($item->is_packed)
                <button class="btn-unpack" onclick="unpackItem({{$item->id}})">
                    <i class="fas fa-box-open"></i>
                    Unpack
                </button>
                @else
                <button class="btn-unreturned" onclick="unreturned({{$item->id}})">
                    <i class="fas fa-redo"></i>
                    Unreturned
                </button>
                @endif
            </div>
        </div>
        @endforeach
    </div>

    <!-- Floating Action Button -->
    <div class="fab-container">
        <button class="fab" id="main-fab" onclick="toggleFab()">
            <i class="fas fa-plus"></i>
        </button>
        <div class="fab-menu" id="fab-menu">
            <button class="fab-item" onclick="openBarcodeScanner()">
                <i class="fas fa-barcode"></i>
            </button>
            <button class="fab-item" onclick="printPackingList4x6()">
                <i class="fas fa-print"></i>
            </button>
            <button class="fab-item" onclick="completeOrder()">
                <i class="fas fa-check"></i>
            </button>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal (Mobile Optimized) -->
<div class="mobile-modal" id="scanner-modal">
    <div class="modal-header">
        <h3>Barcode Scanner</h3>
        <button onclick="closeScannerModal()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="modal-body">
        <div class="scanner-container">
            <video id="mobile-scanner-video"></video>
            <div class="scanner-overlay">
                <div class="scan-line"></div>
            </div>
        </div>
        <div class="scanner-controls">
            <button id="mobile-start-scanner" class="btn-scanner">
                <i class="fas fa-camera"></i> Start Camera
            </button>
            <button id="mobile-stop-scanner" class="btn-scanner" style="display: none;">
                <i class="fas fa-stop"></i> Stop Camera
            </button>
        </div>
        <div class="manual-entry">
            <input type="text" id="mobile-manual-barcode" placeholder="Enter barcode manually">
            <button onclick="searchMobileBarcode()">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div id="mobile-scan-results"></div>
    </div>
</div>

<!-- Success/Error Toast -->
<div class="toast" id="mobile-toast">
    <div class="toast-content">
        <i class="toast-icon"></i>
        <span class="toast-message"></span>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Mobile-First Responsive Design */
    .mobile-pack-container {
        min-height: 100vh;
        background: #f8f9fa;
        padding-bottom: 80px;
    }

    .mobile-header {
        background: #4D734E;
        color: white;
        padding: 10px;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .btn-back, .btn-menu {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn-back:hover, .btn-menu:hover {
        background: rgba(255,255,255,0.1);
    }

    .order-info {
        text-align: center;
    }

    .order-number {
        font-size: 16px;
        font-weight: bold;
    }

    .quote-number {
        font-size: 12px;
        opacity: 0.8;
    }

    .progress-bar {
        background: rgba(255,255,255,0.2);
        height: 6px;
        border-radius: 3px;
        position: relative;
        overflow: hidden;
    }

    .progress-fill {
        background: #fff;
        height: 100%;
        width: 0%;
        transition: width 0.3s ease;
    }

    .progress-text {
        position: absolute;
        top: -25px;
        right: 0;
        font-size: 12px;
        opacity: 0.9;
    }

    .quick-menu {
        background: white;
        padding: 10px;
        display: none;
        flex-direction: row;
        justify-content: space-around;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .quick-menu.show {
        display: flex;
    }

    .menu-item {
        background: none;
        border: none;
        padding: 10px;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        color: #4D734E;
        font-size: 12px;
    }

    .menu-item:hover {
        background: #f0f0f0;
    }

    .menu-item i {
        font-size: 20px;
    }

    .customer-section {
        background: white;
        margin: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .section-header {
        padding: 15px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        border-bottom: 1px solid #e9ecef;
    }

    .section-header .toggle-icon {
        margin-left: auto;
        transition: transform 0.3s ease;
    }

    .section-header.expanded .toggle-icon {
        transform: rotate(180deg);
    }

    .section-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .section-content.show {
        max-height: 300px;
    }

    .customer-details {
        padding: 15px;
        font-size: 14px;
        line-height: 1.5;
    }

    .delivery-instructions {
        margin-top: 10px;
        padding: 10px;
        background: #fff3cd;
        border-radius: 4px;
        border-left: 4px solid #ffc107;
    }

    .items-container {
        padding: 0 10px;
    }

    .mobile-item-card {
        background: white;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .mobile-item-card[data-packed="true"] {
        background: #d4edda;
        border-left: 4px solid #28a745;
    }

    .mobile-item-card.highlight {
        border: 2px solid #4D734E;
        box-shadow: 0 4px 8px rgba(77, 115, 78, 0.3);
    }

    .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .item-number {
        font-weight: bold;
        color: #4D734E;
        font-size: 14px;
    }

    .item-status i {
        font-size: 18px;
    }

    .item-body {
        padding: 15px;
    }

    .item-title {
        font-weight: 500;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .item-color, .item-quantity, .item-box {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 3px;
    }

    .item-actions {
        padding: 10px 15px;
        display: flex;
        gap: 10px;
        background: #f8f9fa;
    }

    .btn-pack, .btn-return, .btn-unpack, .btn-unreturned {
        flex: 1;
        padding: 10px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        transition: all 0.2s ease;
    }

    .btn-pack {
        background: #28a745;
        color: white;
    }

    .btn-pack:hover {
        background: #218838;
    }

    .btn-return {
        background: #dc3545;
        color: white;
    }

    .btn-return:hover {
        background: #c82333;
    }

    .btn-unpack, .btn-unreturned {
        background: #6c757d;
        color: white;
    }

    .btn-unpack:hover, .btn-unreturned:hover {
        background: #5a6268;
    }

    /* Floating Action Button */
    .fab-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .fab {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: #4D734E;
        color: white;
        border: none;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    }

    .fab:hover {
        transform: scale(1.1);
    }

    .fab-menu {
        position: absolute;
        bottom: 70px;
        right: 0;
        display: none;
        flex-direction: column;
        gap: 10px;
    }

    .fab-menu.show {
        display: flex;
    }

    .fab-item {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: white;
        color: #4D734E;
        border: 2px solid #4D734E;
        font-size: 18px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        transition: all 0.2s ease;
    }

    .fab-item:hover {
        background: #4D734E;
        color: white;
    }

    /* Mobile Modal */
    .mobile-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        z-index: 2000;
        display: none;
        flex-direction: column;
    }

    .mobile-modal.show {
        display: flex;
    }

    .mobile-modal .modal-header {
        background: #4D734E;
        color: white;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .mobile-modal .modal-header button {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
    }

    .mobile-modal .modal-body {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
    }

    .scanner-container {
        position: relative;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    #mobile-scanner-video {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }

    .scanner-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 200px;
        height: 100px;
        border: 2px solid #4D734E;
        border-radius: 8px;
        pointer-events: none;
    }

    .scan-line {
        width: 100%;
        height: 2px;
        background: #4D734E;
        position: absolute;
        top: 50%;
        animation: scan 2s linear infinite;
    }

    @keyframes scan {
        0% { top: 0; }
        50% { top: 100%; }
        100% { top: 0; }
    }

    .scanner-controls {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .btn-scanner {
        flex: 1;
        padding: 12px;
        background: #4D734E;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
    }

    .manual-entry {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .manual-entry input {
        flex: 1;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 16px;
    }

    .manual-entry button {
        padding: 12px 16px;
        background: #4D734E;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
    }

    /* Toast Notifications */
    .toast {
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: #333;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        z-index: 3000;
        display: none;
        align-items: center;
        gap: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .toast.show {
        display: flex;
        animation: slideUp 0.3s ease;
    }

    .toast.success {
        background: #28a745;
    }

    .toast.error {
        background: #dc3545;
    }

    @keyframes slideUp {
        from {
            transform: translateX(-50%) translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .mobile-pack-container {
            padding-bottom: 60px;
        }

        .fab {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .fab-item {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }
    }
</style>
@endpush

@push('scripts')
<script src="https://unpkg.com/@zxing/library@latest/umd/index.min.js"></script>
<script>
$(document).ready(function() {
    updateProgress();

    // Initialize mobile interface
    initializeMobileInterface();
});

function initializeMobileInterface() {
    // Auto-hide quick menu after 3 seconds
    setTimeout(() => {
        $('#quick-menu').removeClass('show');
    }, 3000);

    // Touch gestures for item cards
    $('.mobile-item-card').on('touchstart', function(e) {
        $(this).addClass('touching');
    }).on('touchend', function(e) {
        $(this).removeClass('touching');
    });
}

function toggleMenu() {
    $('#quick-menu').toggleClass('show');
}

function toggleSection(sectionId) {
    const header = $(`#${sectionId}-content`).prev('.section-header');
    const content = $(`#${sectionId}-content`);

    header.toggleClass('expanded');
    content.toggleClass('show');
}

function toggleFab() {
    $('#fab-menu').toggleClass('show');
    $('#main-fab i').toggleClass('fa-plus fa-times');
}

function updateProgress() {
    const totalItems = $('.mobile-item-card').length;
    const packedItems = $('.mobile-item-card[data-packed="true"]').length;
    const percentage = totalItems > 0 ? (packedItems / totalItems) * 100 : 0;

    $('#progress-fill').css('width', percentage + '%');
    $('#progress-text').text(`${packedItems} / ${totalItems} items`);
}

function packItem(itemId) {
    showLoading();

    $.ajax({
        url: '{{route("picker.orders.update-item-status")}}',
        method: 'POST',
        data: {
            _token: '{{csrf_token()}}',
            item_id: itemId,
            is_packed: true
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                const itemCard = $(`.mobile-item-card[data-item-id="${itemId}"]`);
                itemCard.attr('data-packed', 'true');
                itemCard.find('.item-status i').removeClass('far fa-circle text-muted').addClass('fas fa-check-circle text-success');

                // Update actions
                itemCard.find('.item-actions').html(`
                    <button class="btn-unpack" onclick="unpackItem(${itemId})">
                        <i class="fas fa-box-open"></i>
                        Unpack
                    </button>
                `);

                updateProgress();
                showToast('Item packed successfully!', 'success');

                // Haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            } else {
                showToast('Failed to pack item', 'error');
            }
        },
        error: function() {
            hideLoading();
            showToast('Error packing item', 'error');
        }
    });
}

function unpackItem(itemId) {
    showLoading();

    $.ajax({
        url: '{{route("picker.orders.update-item-status")}}',
        method: 'POST',
        data: {
            _token: '{{csrf_token()}}',
            item_id: itemId,
            is_packed: false
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                const itemCard = $(`.mobile-item-card[data-item-id="${itemId}"]`);
                itemCard.attr('data-packed', 'false');
                itemCard.find('.item-status i').removeClass('fas fa-check-circle text-success').addClass('far fa-circle text-muted');

                // Update actions
                itemCard.find('.item-actions').html(`
                    <button class="btn-pack" onclick="packItem(${itemId})">
                        <i class="fas fa-box"></i>
                        Pack
                    </button>
                    <button class="btn-return" onclick="returnItem(${itemId})">
                        <i class="fas fa-undo"></i>
                        Return
                    </button>
                `);

                updateProgress();
                showToast('Item unpacked', 'success');

                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            } else {
                showToast('Failed to unpack item', 'error');
            }
        },
        error: function() {
            hideLoading();
            showToast('Error unpacking item', 'error');
        }
    });
}

function returnItem(itemId) {
    showLoading();

    $.ajax({
        url: '{{route("picker.orders.update-item-status")}}',
        method: 'POST',
        data: {
            _token: '{{csrf_token()}}',
            item_id: itemId,
            is_returned: true
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                const itemCard = $(`.mobile-item-card[data-item-id="${itemId}"]`);
                itemCard.find('.item-status i').removeClass('far fa-circle text-muted').addClass('fas fa-times-circle text-danger');

                // Update actions
                itemCard.find('.item-actions').html(`
                    <button class="btn-unreturned" onclick="unreturned(${itemId})">
                        <i class="fas fa-redo"></i>
                        Unreturned
                    </button>
                `);

                showToast('Item returned', 'success');

                if (navigator.vibrate) {
                    navigator.vibrate([50, 50, 50]);
                }
            } else {
                showToast('Failed to return item', 'error');
            }
        },
        error: function() {
            hideLoading();
            showToast('Error returning item', 'error');
        }
    });
}

function unreturned(itemId) {
    showLoading();

    $.ajax({
        url: '{{route("picker.orders.update-item-status")}}',
        method: 'POST',
        data: {
            _token: '{{csrf_token()}}',
            item_id: itemId,
            is_returned: false
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                const itemCard = $(`.mobile-item-card[data-item-id="${itemId}"]`);
                itemCard.find('.item-status i').removeClass('fas fa-times-circle text-danger').addClass('far fa-circle text-muted');

                // Update actions
                itemCard.find('.item-actions').html(`
                    <button class="btn-pack" onclick="packItem(${itemId})">
                        <i class="fas fa-box"></i>
                        Pack
                    </button>
                    <button class="btn-return" onclick="returnItem(${itemId})">
                        <i class="fas fa-undo"></i>
                        Return
                    </button>
                `);

                showToast('Item unreturned', 'success');

                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            } else {
                showToast('Failed to unreturned item', 'error');
            }
        },
        error: function() {
            hideLoading();
            showToast('Error unreturning item', 'error');
        }
    });
}

function completeOrder() {
    const totalItems = $('.mobile-item-card').length;
    const packedItems = $('.mobile-item-card[data-packed="true"]').length;

    if (packedItems < totalItems) {
        if (!confirm(`Only ${packedItems} of ${totalItems} items are packed. Complete anyway?`)) {
            return;
        }
    }

    showLoading();

    $.ajax({
        url: '{{route("picker.orders.complete", $order->id)}}',
        method: 'POST',
        data: {
            _token: '{{csrf_token()}}'
        },
        success: function(response) {
            hideLoading();

            if (response.success) {
                showToast('Order completed successfully!', 'success');

                setTimeout(() => {
                    window.location.href = '{{route("picker.orders")}}';
                }, 2000);
            } else {
                showToast('Failed to complete order', 'error');
            }
        },
        error: function() {
            hideLoading();
            showToast('Error completing order', 'error');
        }
    });
}

function printPackingList4x6() {
    window.open("{{route('picker.orders.packing-list-4x6', $order->id)}}", '_blank');
}

// Barcode Scanner Functions
let mobileBarcodeReader = null;
let mobileCurrentStream = null;

function openBarcodeScanner() {
    $('#scanner-modal').addClass('show');
    initializeMobileBarcodeScanner();
    toggleFab(); // Close FAB menu
}

function closeScannerModal() {
    $('#scanner-modal').removeClass('show');
    stopMobileScanner();
}

async function initializeMobileBarcodeScanner() {
    try {
        mobileBarcodeReader = new ZXing.BrowserMultiFormatReader();
        const cameras = await mobileBarcodeReader.listVideoInputDevices();

        if (cameras.length > 0) {
            $('#mobile-start-scanner').show();
        } else {
            $('#mobile-scan-results').html('<div class="alert alert-warning">No cameras found. Please use manual entry.</div>');
        }
    } catch (err) {
        console.error('Error initializing mobile barcode scanner:', err);
        $('#mobile-scan-results').html('<div class="alert alert-danger">Camera access denied. Please use manual entry.</div>');
    }
}

$('#mobile-start-scanner').click(async function() {
    try {
        const cameras = await mobileBarcodeReader.listVideoInputDevices();
        const selectedDeviceId = cameras[0].deviceId;

        mobileCurrentStream = await mobileBarcodeReader.decodeFromVideoDevice(selectedDeviceId, 'mobile-scanner-video', (result, err) => {
            if (result) {
                handleMobileBarcodeResult(result.text);
            }
        });

        $('#mobile-start-scanner').hide();
        $('#mobile-stop-scanner').show();
    } catch (err) {
        console.error('Error starting mobile scanner:', err);
        showToast('Failed to start camera', 'error');
    }
});

$('#mobile-stop-scanner').click(function() {
    stopMobileScanner();
});

function stopMobileScanner() {
    if (mobileBarcodeReader) {
        mobileBarcodeReader.reset();
    }
    if (mobileCurrentStream) {
        mobileCurrentStream.getTracks().forEach(track => track.stop());
    }
    $('#mobile-start-scanner').show();
    $('#mobile-stop-scanner').hide();
}

function searchMobileBarcode() {
    const barcode = $('#mobile-manual-barcode').val().trim();
    if (barcode) {
        handleMobileBarcodeResult(barcode);
        $('#mobile-manual-barcode').val('');
    }
}

$('#mobile-manual-barcode').keypress(function(e) {
    if (e.which === 13) {
        searchMobileBarcode();
    }
});

function handleMobileBarcodeResult(barcode) {
    // Find matching item
    let foundItem = null;
    $('.mobile-item-card').each(function() {
        const itemNumber = $(this).find('.item-number').text().trim();
        const itemTitle = $(this).find('.item-title').text().trim();

        if (itemNumber === barcode || itemTitle.includes(barcode)) {
            foundItem = {
                element: $(this),
                itemId: $(this).data('item-id'),
                itemNumber: itemNumber,
                title: itemTitle
            };
            return false;
        }
    });

    if (foundItem) {
        // Highlight item
        $('.mobile-item-card').removeClass('highlight');
        foundItem.element.addClass('highlight');

        // Scroll to item
        $('html, body').animate({
            scrollTop: foundItem.element.offset().top - 100
        }, 500);

        // Show result
        $('#mobile-scan-results').html(`
            <div class="alert alert-success">
                <strong>Found!</strong><br>
                ${foundItem.itemNumber}<br>
                <button class="btn-scanner" onclick="packScannedMobileItem(${foundItem.itemId})" style="margin-top: 10px;">
                    Pack This Item
                </button>
            </div>
        `);

        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(100);
        }

        // Audio feedback
        playSuccessBeep();
    } else {
        $('#mobile-scan-results').html(`
            <div class="alert alert-warning">
                <strong>Not Found</strong><br>
                ${barcode}
            </div>
        `);

        if (navigator.vibrate) {
            navigator.vibrate([100, 100, 100]);
        }

        playErrorBeep();
    }
}

function packScannedMobileItem(itemId) {
    packItem(itemId);
    closeScannerModal();
}

function playSuccessBeep() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800;
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
}

function playErrorBeep() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 300;
    oscillator.type = 'sawtooth';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.4);
}

// Utility Functions
function showToast(message, type = 'success') {
    const toast = $('#mobile-toast');
    const icon = type === 'success' ? 'fas fa-check' : 'fas fa-exclamation-triangle';

    toast.removeClass('success error').addClass(type);
    toast.find('.toast-icon').attr('class', `toast-icon ${icon}`);
    toast.find('.toast-message').text(message);

    toast.addClass('show');

    setTimeout(() => {
        toast.removeClass('show');
    }, 3000);
}

function showLoading() {
    // Simple loading state - could be enhanced with a spinner
    $('button').prop('disabled', true);
}

function hideLoading() {
    $('button').prop('disabled', false);
}

// Close modals when clicking outside
$(document).click(function(e) {
    if (!$(e.target).closest('.fab-container').length) {
        $('#fab-menu').removeClass('show');
        $('#main-fab i').removeClass('fa-times').addClass('fa-plus');
    }

    if (!$(e.target).closest('.quick-menu, .btn-menu').length) {
        $('#quick-menu').removeClass('show');
    }
});

// Prevent zoom on double tap
$('*').on('touchend', function(e) {
    e.preventDefault();
});
</script>
@endpush
