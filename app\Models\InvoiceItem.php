<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'product_id',
        'color_id',
        'item_number',
        'product_name',
        'color_name',
        'description',
        'original_quantity',
        'packed_quantity',
        'invoiced_quantity',
        'unit_price',
        'line_total',
        'cart_item_id',
        'order_box_id'
    ];

    /**
     * Relationships
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function color()
    {
        return $this->belongsTo(Color::class, 'color_id');
    }

    public function cartItem()
    {
        return $this->belongsTo(Cart::class, 'cart_item_id');
    }

    public function orderBox()
    {
        return $this->belongsTo(OrderBox::class, 'order_box_id');
    }

    /**
     * Update line total when quantity or price changes
     */
    public function updateLineTotal()
    {
        $this->line_total = $this->invoiced_quantity * $this->unit_price;
        $this->save();

        // Recalculate invoice totals
        $this->invoice->recalculateTotals();
    }

    /**
     * Get quantity difference from original order
     */
    public function getQuantityDifferenceAttribute()
    {
        return $this->invoiced_quantity - $this->original_quantity;
    }

    /**
     * Check if quantity was adjusted from original
     */
    public function wasQuantityAdjusted()
    {
        return $this->invoiced_quantity !== $this->original_quantity;
    }

    /**
     * Check if quantity was adjusted from packed amount
     */
    public function wasPackedQuantityAdjusted()
    {
        return $this->invoiced_quantity !== $this->packed_quantity;
    }
}
