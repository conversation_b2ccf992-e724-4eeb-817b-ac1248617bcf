<?php

/**
 * Example usage of the updated ShipStationService
 * This file demonstrates how to use the new box-based shipping rate functionality
 */

use App\Services\ShipStationService;

class ShipStationServiceUsageExample
{
    private $shipStationService;

    public function __construct()
    {
        $this->shipStationService = new ShipStationService();
    }

    /**
     * Example 1: Get shipping rates for multiple boxes
     */
    public function getMultipleBoxRates()
    {
        // Define shipping addresses
        $fromAddress = [
            'postal_code' => '07666',
            'city' => 'Teaneck',
            'state' => 'NJ',
            'country' => 'US'
        ];

        $toAddress = [
            'postal_code' => '90210',
            'city' => 'Beverly Hills',
            'state' => 'CA',
            'country' => 'US',
            'residential' => true
        ];

        // Define multiple boxes with different dimensions and weights
        $boxes = [
            [
                'length' => 12,
                'width' => 10,
                'height' => 8,
                'weight' => 5.5,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ],
            [
                'length' => 15,
                'width' => 12,
                'height' => 6,
                'weight' => 3.2,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ],
            [
                'length' => 8,
                'width' => 8,
                'height' => 4,
                'weight' => 1.8,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ]
        ];

        // Get rates for all boxes
        $boxRates = $this->shipStationService->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);

        // Display results
        echo "=== SHIPPING RATES FOR MULTIPLE BOXES ===\n";
        foreach ($boxRates as $boxKey => $boxData) {
            echo "\n{$boxKey}:\n";
            echo "Dimensions: {$boxData['box_info']['length']}x{$boxData['box_info']['width']}x{$boxData['box_info']['height']} inches\n";
            echo "Weight: {$boxData['box_info']['weight']} lbs\n";
            
            if (isset($boxData['rates'])) {
                echo "Available rates (sorted by price):\n";
                foreach ($boxData['rates'] as $rate) {
                    echo "  - {$rate['carrier_name']} {$rate['service_name']}: \${$rate['total_cost']}\n";
                }
            } else {
                echo "Error: {$boxData['error']}\n";
            }
        }

        return $boxRates;
    }

    /**
     * Example 2: Get cheapest shipping option across all boxes
     */
    public function getCheapestOption()
    {
        // Use the same setup as Example 1
        $fromAddress = [
            'postal_code' => '07666',
            'city' => 'Teaneck',
            'state' => 'NJ',
            'country' => 'US'
        ];

        $toAddress = [
            'postal_code' => '90210',
            'city' => 'Beverly Hills',
            'state' => 'CA',
            'country' => 'US',
            'residential' => true
        ];

        $boxes = [
            [
                'length' => 12,
                'width' => 10,
                'height' => 8,
                'weight' => 5.5,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ],
            [
                'length' => 15,
                'width' => 12,
                'height' => 6,
                'weight' => 3.2,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ]
        ];

        // Get rates and find cheapest option
        $boxRates = $this->shipStationService->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);
        $cheapestOption = $this->shipStationService->getCheapestShippingOption($boxRates);

        echo "\n=== CHEAPEST SHIPPING OPTION ===\n";
        echo "Total shipping cost: \${$cheapestOption['total_shipping_cost']}\n";
        echo "Box breakdown:\n";
        foreach ($cheapestOption['box_options'] as $boxKey => $option) {
            echo "  {$boxKey}: {$option['carrier_name']} {$option['service_name']} - \${$option['total_cost']}\n";
        }

        return $cheapestOption;
    }

    /**
     * Example 3: Get shipping options grouped by carrier
     */
    public function getCarrierOptions()
    {
        // Use the same setup as previous examples
        $fromAddress = [
            'postal_code' => '07666',
            'city' => 'Teaneck',
            'state' => 'NJ',
            'country' => 'US'
        ];

        $toAddress = [
            'postal_code' => '90210',
            'city' => 'Beverly Hills',
            'state' => 'CA',
            'country' => 'US',
            'residential' => true
        ];

        $boxes = [
            [
                'length' => 12,
                'width' => 10,
                'height' => 8,
                'weight' => 5.5,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ]
        ];

        // Get rates and group by carrier
        $boxRates = $this->shipStationService->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);
        $carrierOptions = $this->shipStationService->getShippingOptionsByCarrier($boxRates);

        echo "\n=== SHIPPING OPTIONS BY CARRIER ===\n";
        foreach ($carrierOptions as $carrierCode => $carrierData) {
            echo "\n{$carrierData['carrier_name']} (Total: \${$carrierData['total_cost']}):\n";
            foreach ($carrierData['boxes'] as $boxKey => $rate) {
                echo "  {$boxKey}: {$rate['service_name']} - \${$rate['total_cost']}\n";
            }
        }

        return $carrierOptions;
    }

    /**
     * Example 4: Validate box data before getting rates
     */
    public function validateAndGetRates()
    {
        $boxes = [
            [
                'length' => 12,
                'width' => 10,
                'height' => 8,
                'weight' => 5.5
            ],
            [
                'length' => 0, // Invalid - will fail validation
                'width' => 12,
                'height' => 6,
                'weight' => 3.2
            ]
        ];

        echo "\n=== BOX VALIDATION EXAMPLE ===\n";
        foreach ($boxes as $index => $box) {
            $isValid = $this->shipStationService->validateBox($box);
            echo "Box " . ($index + 1) . ": " . ($isValid ? "Valid" : "Invalid") . "\n";
            
            if (!$isValid) {
                echo "  Error: Box must have positive values for length, width, height, and weight\n";
            }
        }
    }

    /**
     * Example 5: Using with an existing order (Laravel model)
     */
    public function getOrderShippingRates($order)
    {
        // This example assumes you have an Order model with related boxes
        $boxes = [];
        foreach ($order->boxes as $index => $box) {
            $boxes[] = [
                'length' => $box->length ?: 12,
                'width' => $box->width ?: 12,
                'height' => $box->height ?: 6,
                'weight' => $box->weight ?: 1,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches',
                'box_id' => $box->id
            ];
        }

        $fromAddress = [
            'postal_code' => config('services.shipstation.from_address.postal_code'),
            'city' => config('services.shipstation.from_address.city'),
            'state' => config('services.shipstation.from_address.state'),
            'country' => config('services.shipstation.from_address.country', 'US')
        ];

        $toAddress = [
            'postal_code' => $order->shipping_zip,
            'city' => $order->shipping_city,
            'state' => $order->shipping_state,
            'country' => $order->shipping_country ?? 'US',
            'residential' => true
        ];

        $boxRates = $this->shipStationService->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);
        $cheapestOption = $this->shipStationService->getCheapestShippingOption($boxRates);

        return [
            'box_rates' => $boxRates,
            'cheapest_option' => $cheapestOption,
            'order_id' => $order->id
        ];
    }
}

/**
 * Usage Instructions:
 * 
 * 1. Make sure your .env file has the ShipStation API credentials:
 *    SHIPSTATION_API_KEY=your_api_key
 *    SHIPSTATION_API_SECRET=your_api_secret
 *    SHIPSTATION_FROM_POSTAL_CODE=your_postal_code
 *    SHIPSTATION_FROM_CITY=your_city
 *    SHIPSTATION_FROM_STATE=your_state
 * 
 * 2. Use the service in your controllers:
 *    $shipStationService = new ShipStationService();
 *    $rates = $shipStationService->getShippingRatesForBoxes($from, $to, $boxes);
 * 
 * 3. The service returns rates sorted from least to most expensive for each box
 * 
 * 4. Use helper methods to find cheapest options or group by carrier
 */
